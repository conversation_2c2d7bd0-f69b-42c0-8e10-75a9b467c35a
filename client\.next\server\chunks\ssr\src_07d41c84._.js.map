{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const truncateThought = (text: string, wordLimit: number = 5): string => {\r\n  const words = text.trim().split(/\\s+/);\r\n  if (words.length <= wordLimit) return text;\r\n  return words.slice(0, wordLimit).join(' ') + '...';\r\n};\r\n\r\nexport const setStudentAuthToken = (token: string) => {\r\n  localStorage.setItem('studentToken', token);\r\n};\r\n\r\nexport const getStudentAuthToken = (): string | null => {\r\n  return localStorage.getItem('studentToken');\r\n};\r\n\r\nexport const clearStudentAuthToken = () => {\r\n  localStorage.removeItem('studentToken');\r\n};\r\n\r\nexport const isStudentAuthenticated = (): boolean => {\r\n  return !!getStudentAuthToken();\r\n};"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,kBAAkB,CAAC,MAAc,YAAoB,CAAC;IACjE,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,IAAI,WAAW,OAAO;IACtC,OAAO,MAAM,KAAK,CAAC,GAAG,WAAW,IAAI,CAAC,OAAO;AAC/C;AAEO,MAAM,sBAAsB,CAAC;IAClC,aAAa,OAAO,CAAC,gBAAgB;AACvC;AAEO,MAAM,sBAAsB;IACjC,OAAO,aAAa,OAAO,CAAC;AAC9B;AAEO,MAAM,wBAAwB;IACnC,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,yBAAyB;IACpC,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/label.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as LabelPrimitive from '@radix-ui/react-label';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Label({ className, ...props }: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Label };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAyD;IACtF,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/form.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as LabelPrimitive from '@radix-ui/react-label';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from 'react-hook-form';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { Label } from '@/components/ui/label';\r\n\r\nconst Form = FormProvider;\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName;\r\n};\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue);\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  );\r\n};\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext);\r\n  const itemContext = React.useContext(FormItemContext);\r\n  const { getFieldState } = useFormContext();\r\n  const formState = useFormState({ name: fieldContext.name });\r\n  const fieldState = getFieldState(fieldContext.name, formState);\r\n\r\n  if (!fieldContext) {\r\n    throw new Error('useFormField should be used within <FormField>');\r\n  }\r\n\r\n  const { id } = itemContext;\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  };\r\n};\r\n\r\ntype FormItemContextValue = {\r\n  id: string;\r\n};\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<'div'>) {\r\n  const id = React.useId();\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div data-slot=\"form-item\" className={cn('grid gap-2', className)} {...props} />\r\n    </FormItemContext.Provider>\r\n  );\r\n}\r\n\r\nfunction FormLabel({ className, ...props }: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField();\r\n\r\n  return (\r\n    <Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn('data-[error=true]:text-destructive', className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField();\r\n\r\n  return (\r\n    <Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={!error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`}\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { formDescriptionId } = useFormField();\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { error, formMessageId } = useFormField();\r\n  const body = error ? String(error?.message ?? '') : props.children;\r\n\r\n  if (!body) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn('text-destructive text-sm', className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  );\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;AAhBA;;;;;;;AAkBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAyB,CAAC;AAErE,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAwB,CAAC;AAEnE,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YAAI,aAAU;YAAY,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAa,GAAG,KAAK;;;;;;;;;;;AAGlF;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAyD;IAC1F,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBAAkB,CAAC,QAAQ,GAAG,mBAAmB,GAAG,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAC3F,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<'input'>) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n        'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',\r\n        'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<'textarea'>) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        'border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SelectPrimitive from '@radix-ui/react-select';\r\nimport { CheckIcon, ChevronDownIcon, ChevronUpIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Select({ ...props }: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />;\r\n}\r\n\r\nfunction SelectGroup({ ...props }: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />;\r\n}\r\n\r\nfunction SelectValue({ ...props }: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />;\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = 'default',\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: 'sm' | 'default';\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  );\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = 'popper',\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md',\r\n          position === 'popper' &&\r\n            'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            'p-1',\r\n            position === 'popper' &&\r\n              'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1'\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction SelectLabel({ className, ...props }: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn('text-muted-foreground px-2 py-1.5 text-xs', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  );\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn('bg-border pointer-events-none -mx-1 my-1 h-px', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn('flex cursor-default items-center justify-center py-1', className)}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  );\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn('flex cursor-default items-center justify-center py-1', className)}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  );\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EAAE,GAAG,OAA0D;IAC7E,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return <div data-slot=\"card-content\" className={cn('px-6', className)} {...props} />;\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBAAO,8OAAC;QAAI,aAAU;QAAe,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;AAClF;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/popover.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Popover({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />;\r\n}\r\n\r\nfunction PopoverTrigger({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />;\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = 'center',\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction PopoverAnchor({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />;\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EAAE,GAAG,OAA2D;IAC/E,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EAAE,GAAG,OAA8D;IACzF,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EAAE,GAAG,OAA6D;IACvF,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 724, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/calendar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport { ChevronLeft, ChevronRight } from 'lucide-react';\r\nimport {\r\n  format,\r\n  startOfMonth,\r\n  endOfMonth,\r\n  startOfWeek,\r\n  endOfWeek,\r\n  addDays,\r\n  addMonths,\r\n  subMonths,\r\n  isSameMonth,\r\n  isSameDay,\r\n  isToday\r\n} from 'date-fns';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { Button } from '@/components/ui/button';\r\n\r\ninterface CalendarProps {\r\n  className?: string;\r\n  selected?: Date;\r\n  onSelect?: (date: Date) => void;\r\n  disabled?: (date: Date) => boolean;\r\n  mode?: 'single' | 'range';\r\n  month?: Date;\r\n  onMonthChange?: (date: Date) => void;\r\n  fromYear?: number;\r\n  toYear?: number;\r\n  captionLayout?: 'buttons' | 'dropdown';\r\n  initialFocus?: boolean;\r\n  classNames?: Record<string, string>;\r\n}\r\n\r\nfunction Calendar({\r\n  className,\r\n  selected,\r\n  onSelect,\r\n  disabled,\r\n  month,\r\n  onMonthChange,\r\n  fromYear,\r\n  toYear,\r\n  captionLayout = 'buttons',\r\n  classNames,\r\n  ...props\r\n}: CalendarProps) {\r\n  const [currentMonth, setCurrentMonth] = React.useState(month || selected || new Date());\r\n\r\n  React.useEffect(() => {\r\n    if (month) {\r\n      setCurrentMonth(month);\r\n    }\r\n  }, [month]);\r\n\r\n  const monthStart = startOfMonth(currentMonth);\r\n  const monthEnd = endOfMonth(monthStart);\r\n  const startDate = startOfWeek(monthStart);\r\n  const endDate = endOfWeek(monthEnd);\r\n\r\n  const dateFormat = 'MMMM yyyy';\r\n  const rows = [];\r\n  let days = [];\r\n  let day = startDate;\r\n  let formattedDate = '';\r\n\r\n  // Generate calendar days\r\n  while (day <= endDate) {\r\n    for (let i = 0; i < 7; i++) {\r\n      formattedDate = format(day, 'd');\r\n      const cloneDay = day;\r\n\r\n      days.push(\r\n        <div\r\n          key={day.toString()}\r\n          className={cn(\r\n            'relative p-0 text-center text-sm focus-within:relative focus-within:z-20 cursor-pointer',\r\n            'h-8 w-8 flex items-center justify-center rounded-md hover:bg-accent hover:text-accent-foreground',\r\n            {\r\n              'text-muted-foreground': !isSameMonth(day, monthStart),\r\n              'bg-primary text-primary-foreground': selected && isSameDay(day, selected),\r\n              'bg-accent text-accent-foreground': isToday(day) && (!selected || !isSameDay(day, selected)),\r\n              'opacity-50 cursor-not-allowed': disabled && disabled(day),\r\n            }\r\n          )}\r\n          onClick={() => {\r\n            if (!disabled || !disabled(cloneDay)) {\r\n              onSelect?.(cloneDay);\r\n            }\r\n          }}\r\n        >\r\n          <span className=\"font-normal\">{formattedDate}</span>\r\n        </div>\r\n      );\r\n      day = addDays(day, 1);\r\n    }\r\n    rows.push(\r\n      <div className=\"flex w-full mt-2\" key={day.toString()}>\r\n        {days}\r\n      </div>\r\n    );\r\n    days = [];\r\n  }\r\n\r\n  const nextMonth = () => {\r\n    const newMonth = addMonths(currentMonth, 1);\r\n    setCurrentMonth(newMonth);\r\n    onMonthChange?.(newMonth);\r\n  };\r\n\r\n  const prevMonth = () => {\r\n    const newMonth = subMonths(currentMonth, 1);\r\n    setCurrentMonth(newMonth);\r\n    onMonthChange?.(newMonth);\r\n  };\r\n\r\n  const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\r\n    const newMonth = new Date(currentMonth.getFullYear(), parseInt(e.target.value), 1);\r\n    setCurrentMonth(newMonth);\r\n    onMonthChange?.(newMonth);\r\n  };\r\n\r\n  const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\r\n    const newMonth = new Date(parseInt(e.target.value), currentMonth.getMonth(), 1);\r\n    setCurrentMonth(newMonth);\r\n    onMonthChange?.(newMonth);\r\n  };\r\n\r\n  return (\r\n    <div className={cn('p-3', className)} {...props}>\r\n      <div className=\"flex flex-col gap-4\">\r\n        {/* Header */}\r\n        <div className={cn('flex justify-center pt-1 relative items-center w-full', classNames?.caption)}>\r\n          {captionLayout === 'dropdown' ? (\r\n            <div className=\"flex gap-2\">\r\n              <select\r\n                value={currentMonth.getMonth()}\r\n                onChange={handleMonthChange}\r\n                className={cn('mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white', classNames?.dropdown)}\r\n              >\r\n                {Array.from({ length: 12 }, (_, i) => (\r\n                  <option key={i} value={i}>\r\n                    {format(new Date(2000, i, 1), 'MMMM')}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n              <select\r\n                value={currentMonth.getFullYear()}\r\n                onChange={handleYearChange}\r\n                className={cn('mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white', classNames?.dropdown)}\r\n              >\r\n                {Array.from({ length: (toYear || new Date().getFullYear()) - (fromYear || 1950) + 1 }, (_, i) => {\r\n                  const year = (fromYear || 1950) + i;\r\n                  return (\r\n                    <option key={year} value={year}>\r\n                      {year}\r\n                    </option>\r\n                  );\r\n                })}\r\n              </select>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"absolute left-1 size-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\r\n                onClick={prevMonth}\r\n              >\r\n                <ChevronLeft className=\"size-4\" />\r\n              </Button>\r\n              <div className={cn('text-sm font-medium', classNames?.caption_label)}>\r\n                {format(currentMonth, dateFormat)}\r\n              </div>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"absolute right-1 size-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\r\n                onClick={nextMonth}\r\n              >\r\n                <ChevronRight className=\"size-4\" />\r\n              </Button>\r\n            </>\r\n          )}\r\n        </div>\r\n\r\n        {/* Calendar Grid */}\r\n        <div className=\"w-full border-collapse space-x-1\">\r\n          {/* Days of week header */}\r\n          <div className=\"flex\">\r\n            {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day) => (\r\n              <div\r\n                key={day}\r\n                className=\"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem] text-center\"\r\n              >\r\n                {day}\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          {/* Calendar rows */}\r\n          {rows}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport { Calendar };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AAnBA;;;;;;;AAoCA,SAAS,SAAS,EAChB,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,aAAa,EACb,QAAQ,EACR,MAAM,EACN,gBAAgB,SAAS,EACzB,UAAU,EACV,GAAG,OACW;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE,SAAS,YAAY,IAAI;IAEhF,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,OAAO;YACT,gBAAgB;QAClB;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,aAAa,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE;IAChC,MAAM,WAAW,CAAA,GAAA,yIAAA,CAAA,aAAU,AAAD,EAAE;IAC5B,MAAM,YAAY,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD,EAAE;IAC9B,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE;IAE1B,MAAM,aAAa;IACnB,MAAM,OAAO,EAAE;IACf,IAAI,OAAO,EAAE;IACb,IAAI,MAAM;IACV,IAAI,gBAAgB;IAEpB,yBAAyB;IACzB,MAAO,OAAO,QAAS;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,gBAAgB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,KAAK;YAC5B,MAAM,WAAW;YAEjB,KAAK,IAAI,eACP,8OAAC;gBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,oGACA;oBACE,yBAAyB,CAAC,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD,EAAE,KAAK;oBAC3C,sCAAsC,YAAY,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,KAAK;oBACjE,oCAAoC,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,KAAK,SAAS;oBAC3F,iCAAiC,YAAY,SAAS;gBACxD;gBAEF,SAAS;oBACP,IAAI,CAAC,YAAY,CAAC,SAAS,WAAW;wBACpC,WAAW;oBACb;gBACF;0BAEA,cAAA,8OAAC;oBAAK,WAAU;8BAAe;;;;;;eAjB1B,IAAI,QAAQ;;;;;YAoBrB,MAAM,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,KAAK;QACrB;QACA,KAAK,IAAI,eACP,8OAAC;YAAI,WAAU;sBACZ;WADoC,IAAI,QAAQ;;;;;QAIrD,OAAO,EAAE;IACX;IAEA,MAAM,YAAY;QAChB,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QACzC,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,YAAY;QAChB,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QACzC,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,IAAI,KAAK,aAAa,WAAW,IAAI,SAAS,EAAE,MAAM,CAAC,KAAK,GAAG;QAChF,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW,IAAI,KAAK,SAAS,EAAE,MAAM,CAAC,KAAK,GAAG,aAAa,QAAQ,IAAI;QAC7E,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QAAa,GAAG,KAAK;kBAC7C,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD,YAAY;8BACrF,kBAAkB,2BACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,OAAO,aAAa,QAAQ;gCAC5B,UAAU;gCACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sIAAsI,YAAY;0CAE/J,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAG,GAAG,CAAC,GAAG,kBAC9B,8OAAC;wCAAe,OAAO;kDACpB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,MAAM,GAAG,IAAI;uCADnB;;;;;;;;;;0CAKjB,8OAAC;gCACC,OAAO,aAAa,WAAW;gCAC/B,UAAU;gCACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sIAAsI,YAAY;0CAE/J,MAAM,IAAI,CAAC;oCAAE,QAAQ,CAAC,UAAU,IAAI,OAAO,WAAW,EAAE,IAAI,CAAC,YAAY,IAAI,IAAI;gCAAE,GAAG,CAAC,GAAG;oCACzF,MAAM,OAAO,CAAC,YAAY,IAAI,IAAI;oCAClC,qBACE,8OAAC;wCAAkB,OAAO;kDACvB;uCADU;;;;;gCAIjB;;;;;;;;;;;6CAIJ;;0CACE,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAEzB,8OAAC;gCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB,YAAY;0CACnD,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;;;;;;0CAExB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;8BAOhC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAM;gCAAM;gCAAM;gCAAM;gCAAM;gCAAM;6BAAK,CAAC,GAAG,CAAC,CAAC,oBAC/C,8OAAC;oCAEC,WAAU;8CAET;mCAHI;;;;;;;;;;wBASV;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 991, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/hooks.ts"], "sourcesContent": ["import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';\r\nimport type { RootState, AppDispatch } from './index';\r\n\r\n// Use throughout your app instead of plain `useDispatch` and `useSelector`\r\nexport const useAppDispatch = () => useDispatch<AppDispatch>();\r\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAIO,MAAM,iBAAiB,IAAM,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;AACvC,MAAM,iBAAkD,yJAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 1005, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/ProfileCompletionIndicator.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { User, ArrowRight } from 'lucide-react';\r\nimport { useSelector } from 'react-redux';\r\nimport { RootState } from '@/store';\r\n\r\nconst ProfileCompletionIndicator = () => {\r\n  const router = useRouter();\r\n  const { profileData } = useSelector((state: RootState) => state.studentProfile);\r\n\r\n  // Check if student is logged in\r\n  const isLoggedIn = typeof window !== 'undefined' && localStorage.getItem('studentToken') !== null;\r\n\r\n  // Check if student has any profile data - only check if profile ID exists\r\n  const hasProfile = profileData?.profile?.id !== undefined;\r\n\r\n  // Only show the indicator if student is logged in and doesn't have a profile\r\n  if (!isLoggedIn || hasProfile) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"my-4 mx-10 sm:px-4\">\r\n      <div className=\"bg-white dark:bg-gray-900 border border-orange-200 overflow-hidden dark:border-orange-900\">\r\n        <div className=\"px-3 py-1.5 flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <div className=\"bg-[#ff914d] p-1.5 rounded-full\">\r\n            <User className=\"h-3 w-3 text-white\" />\r\n          </div>\r\n          <p className=\"text-xs font-medium text-gray-800 dark:text-gray-200\">\r\n            Please Complete your profile \r\n          </p>\r\n        </div>\r\n        <Button\r\n          onClick={() => router.push('/student/profile')}\r\n          className=\"bg-[#ff914d] hover:bg-[#e07c3a] text-white text-xs px-2 py-0.5 h-6 min-w-0\"\r\n          size=\"sm\"\r\n        >\r\n         Complete now <ArrowRight className=\"h-3 w-3\" />\r\n        </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfileCompletionIndicator;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;;AASA,MAAM,6BAA6B;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,cAAc;IAE9E,gCAAgC;IAChC,MAAM,aAAa,gBAAkB,eAAe,aAAa,OAAO,CAAC,oBAAoB;IAE7F,0EAA0E;IAC1E,MAAM,aAAa,aAAa,SAAS,OAAO;IAEhD,6EAA6E;IAC7E,wCAA+B;QAC7B,OAAO;IACT;;AAyBF;uCAEe", "debugId": null}}, {"offset": {"line": 1038, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn(\"text-lg font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: \"outline\" }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAEA;AACA;AANA;;;;;AAQA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,8OAAC,2KAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,2KAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,2KAAA,CAAA,UAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,2KAAA,CAAA,UAA4B;gBAC3B,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,2KAAA,CAAA,QAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,2KAAA,CAAA,cAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1198, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/notificationService.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport interface Notification {\r\n  id: string;\r\n  userId: string;\r\n  userType: 'STUDENT' | 'CLASS' | 'ADMIN';\r\n  type: 'STUDENT_ACCOUNT_CREATED' | 'STUDENT_PROFILE_APPROVED' | 'STUDENT_PROFILE_REJECTED' |\r\n        'STUDENT_COIN_PURCHASE' | 'STUDENT_UWHIZ_PARTICIPATION' | 'STUDENT_CHAT_MESSAGE' |\r\n        'CLASS_ACCOUNT_CREATED' | 'CLASS_PROFILE_APPROVED' | 'CLASS_PROFILE_REJECTED' |\r\n        'CLASS_COIN_PURCHASE' | 'CLASS_CHAT_MESSAGE' | 'CLASS_CONTENT_APPROVED' | 'CLASS_CONTENT_REJECTED' |\r\n        'CLASS_EDUCATION_ADDED' | 'CLASS_EXPERIENCE_ADDED' | 'CLASS_CERTIFICATE_ADDED' |\r\n        'ADMIN_NEW_STUDENT_REGISTRATION' | 'ADMIN_NEW_CLASS_REGISTRATION' |\r\n        'ADMIN_PROFILE_REVIEW_REQUIRED' | 'ADMIN_CONTENT_REVIEW_REQUIRED';\r\n  title: string;\r\n  message: string;\r\n  data?: any;\r\n  isRead: boolean;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface NotificationPagination {\r\n  currentPage: number;\r\n  totalPages: number;\r\n  totalCount: number;\r\n  limit: number;\r\n  hasNextPage: boolean;\r\n  hasPrevPage: boolean;\r\n}\r\n\r\nexport interface NotificationResponse {\r\n  notifications: Notification[];\r\n  pagination: NotificationPagination;\r\n}\r\n\r\n// For Classes (authenticated users)\r\nexport const getClassNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {\r\n  const response = await axiosInstance.get(`/notifications/classes?page=${page}&limit=${limit}`);\r\n  return response.data.data;\r\n};\r\n\r\n\r\n\r\nexport const getClassUnreadCount = async (): Promise<number> => {\r\n  const response = await axiosInstance.get('/notifications/classes/count');\r\n  return response.data.data.count;\r\n};\r\n\r\nexport const markClassNotificationAsRead = async (notificationId: string) => {\r\n  const response = await axiosInstance.post(`/notifications/classes/mark-read/${notificationId}`);\r\n  return response.data;\r\n};\r\n\r\nexport const markAllClassNotificationsAsRead = async () => {\r\n  const response = await axiosInstance.post('/notifications/classes/mark-all-read');\r\n  return response.data;\r\n};\r\n\r\nexport const deleteAllClassNotifications = async () => {\r\n  const response = await axiosInstance.delete('/notifications/classes/delete-all');\r\n  return response.data;\r\n};\r\n\r\n// For Students (bearer token auth)\r\nexport const getStudentNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {\r\n  const response = await axiosInstance.get(`/notifications/students?page=${page}&limit=${limit}`);\r\n  return response.data.data;\r\n};\r\n\r\n\r\n\r\nexport const getStudentUnreadCount = async (): Promise<number> => {\r\n  const response = await axiosInstance.get('/notifications/students/count');\r\n  return response.data.data.count;\r\n};\r\n\r\nexport const markStudentNotificationAsRead = async (notificationId: string) => {\r\n  const response = await axiosInstance.post(`/notifications/students/mark-read/${notificationId}`);\r\n  return response.data;\r\n};\r\n\r\nexport const markAllStudentNotificationsAsRead = async () => {\r\n  const response = await axiosInstance.post('/notifications/students/mark-all-read');\r\n  return response.data;\r\n};\r\n\r\nexport const deleteAllStudentNotifications = async () => {\r\n  const response = await axiosInstance.delete('/notifications/students/delete-all');\r\n  return response.data;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAoCO,MAAM,wBAAwB,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IAC9E,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC7F,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAIO,MAAM,sBAAsB;IACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACzC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;AACjC;AAEO,MAAM,8BAA8B,OAAO;IAChD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,iCAAiC,EAAE,gBAAgB;IAC9F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kCAAkC;IAC7C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;IAC1C,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,8BAA8B;IACzC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;IAC5C,OAAO,SAAS,IAAI;AACtB;AAGO,MAAM,0BAA0B,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IAChF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC9F,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAIO,MAAM,wBAAwB;IACnC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACzC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;AACjC;AAEO,MAAM,gCAAgC,OAAO;IAClD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,kCAAkC,EAAE,gBAAgB;IAC/F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,oCAAoC;IAC/C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;IAC1C,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,gCAAgC;IAC3C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;IAC5C,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 1258, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/NotificationBell.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { Bell } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\n\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\nimport {\r\n  getClassNotifications,\r\n  getClassUnreadCount,\r\n  markClassNotificationAsRead,\r\n  markAllClassNotificationsAsRead,\r\n  deleteAllClassNotifications,\r\n  getStudentNotifications,\r\n  getStudentUnreadCount,\r\n  markStudentNotificationAsRead,\r\n  markAllStudentNotificationsAsRead,\r\n  deleteAllStudentNotifications,\r\n  Notification\r\n} from '@/services/notificationService';\r\nimport { toast } from 'sonner';\r\nimport { formatDistanceToNow } from 'date-fns';\r\nimport { useRouter } from 'next/navigation';\r\n\r\ninterface NotificationBellProps {\r\n  userType: 'class' | 'student';\r\n}\r\n\r\nexport default function NotificationBell({ userType }: NotificationBellProps) {\r\n  const [notifications, setNotifications] = useState<Notification[]>([]);\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n\r\n  const router = useRouter();\r\n\r\n  const safeNotifications = Array.isArray(notifications) ? notifications : [];\r\n\r\n  const fetchNotifications = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      let result: any;\r\n      let count: number;\r\n\r\n      if (userType === 'class') {\r\n        result = await getClassNotifications(1, 20);\r\n        count = await getClassUnreadCount();\r\n      } else {\r\n        result = await getStudentNotifications(1, 20);\r\n        count = await getStudentUnreadCount();\r\n      }\r\n\r\n      // Handle both old and new response formats\r\n      const notifs = result?.notifications || result || [];\r\n      setNotifications(Array.isArray(notifs) ? notifs : []);\r\n      setUnreadCount(count);\r\n    } catch (error) {\r\n      console.error('Error fetching notifications:', error);\r\n      setNotifications([]);\r\n      setUnreadCount(0);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [userType]);\r\n\r\n  const handleNotificationClick = async (notification: Notification) => {\r\n    try {\r\n      // Mark notification as read\r\n      if (userType === 'class') {\r\n        await markClassNotificationAsRead(notification.id);\r\n      } else {\r\n        await markStudentNotificationAsRead(notification.id);\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notif =>\r\n          notif.id === notification.id ? { ...notif, isRead: true } : notif\r\n        )\r\n      );\r\n      setUnreadCount(prev => Math.max(0, prev - 1));\r\n      setIsOpen(false);\r\n      if (notification.data?.actionType === 'OPEN_CHAT' && notification.data?.redirectUrl) {\r\n        router.push(notification.data.redirectUrl);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error handling notification click:', error);\r\n      toast.error('Failed to process notification');\r\n    }\r\n  };\r\n\r\n  const handleMarkAllAsRead = async () => {\r\n    try {\r\n      if (userType === 'class') {\r\n        await markAllClassNotificationsAsRead();\r\n      } else {\r\n        await markAllStudentNotificationsAsRead();\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notif => ({ ...notif, isRead: true }))\r\n      );\r\n      setUnreadCount(0);\r\n      toast.success('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      toast.error('Failed to mark all notifications as read');\r\n    }\r\n  };\r\n\r\n  const handleRemoveAllClick = () => {\r\n    setShowDeleteDialog(true);\r\n  };\r\n\r\n  const handleConfirmRemoveAll = async () => {\r\n    setShowDeleteDialog(false);\r\n\r\n    try {\r\n      if (userType === 'class') {\r\n        await deleteAllClassNotifications();\r\n      } else {\r\n        await deleteAllStudentNotifications();\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications([]);\r\n      setUnreadCount(0);\r\n      toast.success('All notifications removed successfully');\r\n    } catch (error) {\r\n      console.error('Error removing all notifications:', error);\r\n      toast.error('Failed to remove all notifications');\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchNotifications();\r\n\r\n    const interval = setInterval(fetchNotifications, 30000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [fetchNotifications]);\r\n\r\n  return (\r\n    <>\r\n      <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          className=\"relative group rounded-full border-2 border-orange-500 hover:border-orange-400 bg-black hover:bg-gray-900 transition-all duration-200 h-8 w-8 md:h-10 md:w-10\"\r\n        >\r\n          <div className=\"absolute rounded-full inset-0 bg-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200\" />\r\n          <div className=\"relative z-10 flex items-center justify-center\">\r\n            <Bell className=\"h-4 w-4 md:h-5 md:w-5 text-orange-500 group-hover:text-orange-400 transition-colors duration-200\" />\r\n            {unreadCount > 0 && (\r\n              <div className=\"absolute -top-1 -right-1 md:-top-2 md:-right-2 h-4 w-4 md:h-5 md:w-5 bg-red-500 rounded-full flex items-center justify-center border-2 border-white\">\r\n                <span className=\"text-white text-[10px] md:text-xs font-bold leading-none\">\r\n                  {unreadCount > 99 ? '99+' : unreadCount}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-80 p-0\" align=\"end\">\r\n        <div className=\"p-4 border-b\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h3 className=\"font-semibold\">Notifications</h3>\r\n            <div className=\"flex gap-2\">\r\n              {unreadCount > 0 && (\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={handleMarkAllAsRead}\r\n                  className=\"text-xs\"\r\n                >\r\n                  Mark all read\r\n                </Button>\r\n              )}\r\n              {notifications.length > 0 && unreadCount === 0 && (\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={handleRemoveAllClick}\r\n                  className=\"text-xs text-red-600 hover:text-red-700 hover:bg-red-50\"\r\n                >\r\n                  Remove all\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"h-80 overflow-y-auto\">\r\n          {loading ? (\r\n            <div className=\"p-4 text-center text-muted-foreground\">\r\n              Loading notifications...\r\n            </div>\r\n          ) : notifications.length === 0 ? (\r\n            <div className=\"p-4 text-center text-muted-foreground\">\r\n              No notifications yet\r\n            </div>\r\n          ) : (\r\n            <div className=\"divide-y\">\r\n              {Array.isArray(notifications) && notifications.map((notification) => (\r\n                <div\r\n                  key={notification.id}\r\n                  className={`p-4 cursor-pointer hover:bg-muted/50 transition-colors ${\r\n                    !notification.isRead ? 'bg-blue-50/50' : ''\r\n                  }`}\r\n                  onClick={() => handleNotificationClick(notification)}\r\n                >\r\n                  <div className=\"flex items-start gap-3\">\r\n                    <div className={`w-2 h-2 rounded-full mt-2 ${\r\n                      !notification.isRead ? 'bg-blue-500' : 'bg-gray-300'\r\n                    }`} />\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      <p className=\"font-medium text-sm\">{notification.title}</p>\r\n                      <p className=\"text-sm text-muted-foreground mt-1\">\r\n                        {notification.message}\r\n                      </p>\r\n                      <p className=\"text-xs text-muted-foreground mt-2\">\r\n                        {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n        {safeNotifications.length > 0 && (\r\n          <div className=\"p-3 border-t bg-muted/30\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"w-full text-xs\"\r\n              onClick={() => {\r\n                setIsOpen(false);\r\n                router.push('/notifications');\r\n              }}\r\n            >\r\n              View All Notifications\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </PopoverContent>\r\n    </Popover>\r\n\r\n    {/* Delete Confirmation Dialog */}\r\n    <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>Remove All Notifications</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            Are you sure you want to remove all notifications? This action cannot be undone.\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n          <AlertDialogAction\r\n            onClick={handleConfirmRemoveAll}\r\n            className=\"bg-red-600 hover:bg-red-700\"\r\n          >\r\n            Remove All\r\n          </AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAMA;AAUA;AAaA;AACA;AACA;AApCA;;;;;;;;;;;AA0Ce,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;IAC1E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,oBAAoB,MAAM,OAAO,CAAC,iBAAiB,gBAAgB,EAAE;IAE3E,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI;YACF,WAAW;YACX,IAAI;YACJ,IAAI;YAEJ,IAAI,aAAa,SAAS;gBACxB,SAAS,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD,EAAE,GAAG;gBACxC,QAAQ,MAAM,CAAA,GAAA,sIAAA,CAAA,sBAAmB,AAAD;YAClC,OAAO;gBACL,SAAS,MAAM,CAAA,GAAA,sIAAA,CAAA,0BAAuB,AAAD,EAAE,GAAG;gBAC1C,QAAQ,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD;YACpC;YAEA,2CAA2C;YAC3C,MAAM,SAAS,QAAQ,iBAAiB,UAAU,EAAE;YACpD,iBAAiB,MAAM,OAAO,CAAC,UAAU,SAAS,EAAE;YACpD,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,iBAAiB,EAAE;YACnB,eAAe;QACjB,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,0BAA0B,OAAO;QACrC,IAAI;YACF,4BAA4B;YAC5B,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,sIAAA,CAAA,8BAA2B,AAAD,EAAE,aAAa,EAAE;YACnD,OAAO;gBACL,MAAM,CAAA,GAAA,sIAAA,CAAA,gCAA6B,AAAD,EAAE,aAAa,EAAE;YACrD;YAEA,qBAAqB;YACrB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,EAAE,KAAK,aAAa,EAAE,GAAG;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAK,IAAI;YAGhE,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;YAC1C,UAAU;YACV,IAAI,aAAa,IAAI,EAAE,eAAe,eAAe,aAAa,IAAI,EAAE,aAAa;gBACnF,OAAO,IAAI,CAAC,aAAa,IAAI,CAAC,WAAW;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,sIAAA,CAAA,kCAA+B,AAAD;YACtC,OAAO;gBACL,MAAM,CAAA,GAAA,sIAAA,CAAA,oCAAiC,AAAD;YACxC;YAEA,qBAAqB;YACrB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAK,CAAC;YAE/C,eAAe;YACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,uBAAuB;QAC3B,oBAAoB;IACtB;IAEA,MAAM,yBAAyB;QAC7B,oBAAoB;QAEpB,IAAI;YACF,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,sIAAA,CAAA,8BAA2B,AAAD;YAClC,OAAO;gBACL,MAAM,CAAA,GAAA,sIAAA,CAAA,gCAA6B,AAAD;YACpC;YAEA,qBAAqB;YACrB,iBAAiB,EAAE;YACnB,eAAe;YACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,MAAM,WAAW,YAAY,oBAAoB;QAEjD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAmB;IAEvB,qBACE;;0BACE,8OAAC,mIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAQ,cAAc;;kCACrC,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,cAAc,mBACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOxC,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAW,OAAM;;0CACzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAgB;;;;;;sDAC9B,8OAAC;4CAAI,WAAU;;gDACZ,cAAc,mBACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;gDAIF,cAAc,MAAM,GAAG,KAAK,gBAAgB,mBAC3C,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAOT,8OAAC;gCAAI,WAAU;0CACZ,wBACC,8OAAC;oCAAI,WAAU;8CAAwC;;;;;2CAGrD,cAAc,MAAM,KAAK,kBAC3B,8OAAC;oCAAI,WAAU;8CAAwC;;;;;yDAIvD,8OAAC;oCAAI,WAAU;8CACZ,MAAM,OAAO,CAAC,kBAAkB,cAAc,GAAG,CAAC,CAAC,6BAClD,8OAAC;4CAEC,WAAW,CAAC,uDAAuD,EACjE,CAAC,aAAa,MAAM,GAAG,kBAAkB,IACzC;4CACF,SAAS,IAAM,wBAAwB;sDAEvC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,0BAA0B,EACzC,CAAC,aAAa,MAAM,GAAG,gBAAgB,eACvC;;;;;;kEACF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAuB,aAAa,KAAK;;;;;;0EACtD,8OAAC;gEAAE,WAAU;0EACV,aAAa,OAAO;;;;;;0EAEvB,8OAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,aAAa,SAAS,GAAG;oEAAE,WAAW;gEAAK;;;;;;;;;;;;;;;;;;2CAhB1E,aAAa,EAAE;;;;;;;;;;;;;;;4BAyB7B,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP,UAAU;wCACV,OAAO,IAAI,CAAC;oCACd;8CACD;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAI1B,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQX", "debugId": null}}, {"offset": {"line": 1697, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/avatar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Avatar({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn('relative flex size-8 shrink-0 overflow-hidden rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarImage({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn('aspect-square size-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn('bg-muted flex size-full items-center justify-center rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EAAE,SAAS,EAAE,GAAG,OAA0D;IACxF,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;QAC3E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;QACjF,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1749, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/studentAuthServices.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\nimport { GoogleAuthData } from '@/lib/types';\r\n\r\ninterface StudentRegisterData {\r\n  firstName: string;\r\n  lastName: string;\r\n  contactNo: string;\r\n  referralCode?: string;\r\n}\r\n\r\ninterface StudentLoginData {\r\n  contactNo: string;\r\n  email?: string;\r\n}\r\n\r\ninterface VerifyOtpData {\r\n  contactNo: string;\r\n  otp: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n  email?: string;\r\n}\r\n\r\ninterface ResendOtpData {\r\n  contactNo: string;\r\n  firstName?: string;\r\n}\r\n\r\ninterface ContinueWithEmailData {\r\n  email: string;\r\n}\r\n\r\nexport const continueWithEmail = async (data: ContinueWithEmailData) => {\r\n  const response = await axiosInstance.post('/student/continue-with-email', data);\r\n  return response.data;\r\n};\r\n\r\nexport const registerStudent = async (data: StudentRegisterData) => {\r\n  const response = await axiosInstance.post('/student/register', data);\r\n  return response.data;\r\n};\r\n\r\nexport const loginStudent = async (data: StudentLoginData) => {\r\n  const response = await axiosInstance.post('/student/login', data);\r\n  return response.data;\r\n};\r\n\r\nexport const logoutStudent = async (): Promise<any> => {\r\n  localStorage.removeItem('studentToken');\r\n  localStorage.removeItem('student_data');\r\n  return {\r\n    success: true,\r\n    message: 'Logged out successfully',\r\n  };\r\n};\r\n\r\nexport async function verifyOtp(data: VerifyOtpData) {\r\n  const response = await axiosInstance.post('/student/verify-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function resendOtp(data: ResendOtpData) {\r\n  const response = await axiosInstance.post('/student/resend-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport const googleAuthStudent = async (googleAuthData: GoogleAuthData) => {\r\n    const response = await axiosInstance.post(`/student/google-auth`, googleAuthData);\r\n    return response.data;\r\n};\r\n\r\nexport const studentverifyEmail = async (token: string) => {\r\n    const response = await axiosInstance.post(`/student/verify-email`, { token });\r\n    return response.data;\r\n};\r\n\r\nexport const studentresendVerificationEmail = async (email: string) => {\r\n    const response = await axiosInstance.post(`/student/resend-verification`, { email });\r\n    return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAgCO,MAAM,oBAAoB,OAAO;IACtC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,gCAAgC;IAC1E,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,qBAAqB;IAC/D,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,eAAe,OAAO;IACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,kBAAkB;IAC5D,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,gBAAgB;IAC3B,aAAa,UAAU,CAAC;IACxB,aAAa,UAAU,CAAC;IACxB,OAAO;QACL,SAAS;QACT,SAAS;IACX;AACF;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;IACjE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;IACjE,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,oBAAoB,OAAO;IACpC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE;IAClE,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,qBAAqB,OAAO;IACrC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,EAAE;QAAE;IAAM;IAC3E,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,iCAAiC,OAAO;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAAC,EAAE;QAAE;IAAM;IAClF,OAAO,SAAS,IAAI;AACxB", "debugId": null}}, {"offset": {"line": 1812, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/AuthService.ts"], "sourcesContent": ["import { axiosInstance } from '../lib/axios';\r\n\r\ninterface RegisterData {\r\n  firstName: string;\r\n  lastName: string;\r\n  contactNo: string;\r\n  referralCode?: string;\r\n}\r\n\r\ninterface LoginData {\r\n  contactNo: string;\r\n  email?: string;\r\n}\r\n\r\ninterface VerifyOtpData {\r\n  contactNo: string;\r\n  otp: string;\r\n  email?: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n}\r\n\r\ninterface ResendOtpData {\r\n  contactNo: string;\r\n  firstName?: string;\r\n}\r\n\r\ninterface ContinueWithEmailData {\r\n  email: string;\r\n}\r\n\r\nexport async function continueWithEmail(data: ContinueWithEmailData) {\r\n  const response = await axiosInstance.post('/auth-client/continue-with-email', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function registerUser(data: RegisterData) {\r\n  const response = await axiosInstance.post('/auth-client/register', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function loginUser(data: LoginData) {\r\n  const response = await axiosInstance.post('/auth-client/login', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function verifyOtp(data: VerifyOtpData) {\r\n  const response = await axiosInstance.post('/auth-client/verify-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function resendOtp(data: ResendOtpData) {\r\n  const response = await axiosInstance.post('/auth-client/resend-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport function logoutUser(): void {\r\n  localStorage.removeItem('user');\r\n}\r\n\r\nexport const generateJWT = async (contact: string | undefined, password : string | undefined) => {\r\n  const response = await axiosInstance.post(`/auth-client/generate-jwt`, { contact, password });\r\n  return response.data;\r\n};\r\n\r\n\r\n\r\nexport const verifyEmail = async (token: string) => {\r\n  const response = await axiosInstance.get(`/auth-client/verify-email`, { params: { token } });\r\n  return response.data;\r\n};\r\n\r\nexport const resendVerificationEmail = async (email: string) => {\r\n  const response = await axiosInstance.post(`/auth-client/resend-verification`, { email });\r\n  return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AA+BO,eAAe,kBAAkB,IAA2B;IACjE,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,oCAAoC;IAC9E,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,aAAa,IAAkB;IACnD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,yBAAyB;IACnE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAe;IAC7C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,sBAAsB;IAChE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,2BAA2B;IACrE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,2BAA2B;IACrE,OAAO,SAAS,IAAI;AACtB;AAEO,SAAS;IACd,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,cAAc,OAAO,SAA6B;IAC7D,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAE;QAAS;IAAS;IAC3F,OAAO,SAAS,IAAI;AACtB;AAIO,MAAM,cAAc,OAAO;IAChC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAE,QAAQ;YAAE;QAAM;IAAE;IAC1F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,gCAAgC,CAAC,EAAE;QAAE;IAAM;IACtF,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 1875, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/mockExamStreakApi.ts"], "sourcesContent": ["import { axiosInstance } from \"../lib/axios\";\r\n\r\nexport const saveMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.put(`/mock-exam-streak/${studentId}`, {}, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to save mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const getMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.get(`/mock-exam-streak/${studentId}`, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data }; \r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,EAAE,CAAC,GAAG;YAC7E,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,iCAAiC,EACvC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF;AAEO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,EAAE;YACzE,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gCAAgC,EACtC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF", "debugId": null}}, {"offset": {"line": 1923, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/streakcountdisplay.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { getMockExamStreak } from '@/services/mockExamStreakApi';\r\n\r\n\r\ninterface MockExamStreakResponse {\r\n  success: boolean;\r\n  data?: { streak: number; lastAttempt: string | null };\r\n  error?: string;\r\n}\r\n\r\ninterface StreakDisplayProps {\r\n  studentId?: string;\r\n}\r\n\r\nconst StreakDisplay: React.FC<StreakDisplayProps> = ({ studentId }) => {\r\n  const [streak, setStreak] = useState<number>(0);\r\n\r\n  useEffect(() => {\r\n    const fetchStreak = async () => {\r\n      if (!studentId) {\r\n        setStreak(0);\r\n        return;\r\n      }\r\n      const response: MockExamStreakResponse = await getMockExamStreak(studentId);\r\n      if (response.success && response.data) {\r\n        setStreak(response.data.streak || 0);\r\n      } else {\r\n        setStreak(0);\r\n      }\r\n    };\r\n    fetchStreak();\r\n  }, [studentId]);\r\n\r\n  return (\r\n       <span className=\"bg-black  text-white text-l rounded-full px-2 py-1 flex items-center gap-1\">\r\n      🔥 {streak}\r\n    </span>\r\n  );\r\n};\r\n\r\nexport default StreakDisplay;"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAaA,MAAM,gBAA8C,CAAC,EAAE,SAAS,EAAE;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,IAAI,CAAC,WAAW;gBACd,UAAU;gBACV;YACF;YACA,MAAM,WAAmC,MAAM,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EAAE;YACjE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,UAAU,SAAS,IAAI,CAAC,MAAM,IAAI;YACpC,OAAO;gBACL,UAAU;YACZ;QACF;QACA;IACF,GAAG;QAAC;KAAU;IAEd,qBACK,8OAAC;QAAK,WAAU;;YAA6E;YAC1F;;;;;;;AAGV;uCAEe", "debugId": null}}, {"offset": {"line": 1970, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { Menu, X, User, ShoppingBag, Briefcase, Share2, UserCircle, LayoutDashboard, BadgeCent, MessageSquare, GraduationCap, Flame, ShoppingBagIcon } from \"lucide-react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { RootState } from \"@/store\";\r\nimport { useAppDispatch } from \"@/store/hooks\";\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport { isStudentAuthenticated, clearStudentAuthToken } from \"@/lib/utils\";\r\nimport ProfileCompletionIndicator from \"./ProfileCompletionIndicator\";\r\nimport NotificationBell from \"./NotificationBell\";\r\nimport {\r\n  Avatar,\r\n  AvatarFallback,\r\n} from \"@/components/ui/avatar\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { toast } from \"sonner\";\r\nimport { logoutStudent } from \"@/services/studentAuthServices\";\r\nimport { clearUser } from \"@/store/slices/userSlice\";\r\nimport { clearStudentProfileData } from \"@/store/slices/studentProfileSlice\";\r\nimport { fetchStudentProfile } from \"@/store/thunks/studentProfileThunks\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { axiosInstance } from \"@/lib/axios\";\r\nimport { useMotionValue, useAnimationFrame } from \"framer-motion\";\r\nimport { generateJWT } from \"@/services/AuthService\";\r\nimport StreakDisplay from \"@/components/ui/streakcountdisplay\";\r\n\r\n\r\n\r\nconst Header = () => {\r\n  const { isAuthenticated, user } = useSelector((state: RootState) => state.user);\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);\r\n  const [studentData, setStudentData] = useState<any>(null);\r\n  const dispatch = useAppDispatch();\r\n  const router = useRouter();\r\n\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n  const [contentWidth, setContentWidth] = useState(0);\r\n  const x = useMotionValue(0);\r\n  const speed = contentWidth / 20;\r\n \r\n\r\n  useEffect(() => {\r\n    const isLoggedIn = isStudentAuthenticated();\r\n    setIsStudentLoggedIn(isLoggedIn);\r\n\r\n    if (isLoggedIn) {\r\n      const storedData = localStorage.getItem('student_data');\r\n      if (storedData) {\r\n        setStudentData(JSON.parse(storedData));\r\n      }\r\n      dispatch(fetchStudentProfile());\r\n    }\r\n\r\n    const handleStorageChange = () => {\r\n      const newLoginStatus = isStudentAuthenticated();\r\n      setIsStudentLoggedIn(newLoginStatus);\r\n      if (newLoginStatus) {\r\n        const storedData = localStorage.getItem('student_data');\r\n        if (storedData) {\r\n          setStudentData(JSON.parse(storedData));\r\n        }\r\n        dispatch(fetchStudentProfile());\r\n      } else {\r\n        setStudentData(null);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('storage', handleStorageChange);\r\n\r\n    if (contentRef.current) {\r\n      const width = contentRef.current.getBoundingClientRect().width;\r\n      setContentWidth(width);\r\n    }\r\n\r\n    return () => {\r\n      window.removeEventListener('storage', handleStorageChange);\r\n    };\r\n  }, [dispatch]);\r\n\r\n  useAnimationFrame((time, delta) => {\r\n    if (contentWidth === 0) return;\r\n    const currentX = x.get();\r\n    const deltaX = (speed * delta) / 1000;\r\n    let newX = currentX - deltaX;\r\n    if (newX <= -contentWidth) {\r\n      newX = 0;\r\n    }\r\n    x.set(newX);\r\n  });\r\n\r\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\r\n\r\n  const handleStudentLogout = async () => {\r\n    try {\r\n      const response = await logoutStudent();\r\n      if (response.success !== false) {\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem('student_data');\r\n        dispatch(clearStudentProfileData());\r\n        toast.success(\"Logged out successfully\");\r\n        window.dispatchEvent(new Event('storage'));\r\n      } else {\r\n        toast.error(response.message || \"Failed to logout\");\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem('student_data');\r\n        dispatch(clearStudentProfileData());\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Failed to logout\", error);\r\n      toast.error(\"Failed to logout\");\r\n      localStorage.removeItem('student_data');\r\n      clearStudentAuthToken();\r\n      setIsStudentLoggedIn(false);\r\n      setStudentData(null);\r\n      dispatch(clearStudentProfileData());\r\n    }\r\n  };\r\n\r\n  const accessClassDashboard = async () => {\r\n    try {\r\n      const response = await generateJWT(user?.contactNo, user?.password);\r\n\r\n      if (response.success) {\r\n        const { token } = response.data;\r\n        const redirectUrl = `${process.env.NEXT_PUBLIC_RANNDASS_URL}/login-class-link?uid=${user?.id}&token=${token}`;\r\n        window.location.href = redirectUrl;\r\n      } else {\r\n        toast.error(response.message || \"Failed to generate token\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Failed to generate token\", error);\r\n      toast.error(\"Failed to generate token\");\r\n    }\r\n  };\r\n\r\n  const navLinks = [\r\n    { href: \"/verified-classes\", label: \"Find Tutor\", icon: <GraduationCap className=\"w-4 h-4\" /> },\r\n    { href: \"/uwhiz\", label: \"U - Whiz\", icon: <Flame className=\"w-4 h-4\" />, isNew: true },\r\n    {\r\n      href: \"/mock-exam-card\",\r\n      label: (\r\n        <span className=\"flex items-center gap-2\">\r\n          <span>Daily Quiz</span>\r\n          {isStudentLoggedIn && <StreakDisplay studentId={studentData?.id} />}\r\n        </span>\r\n      ),\r\n      icon: <BadgeCent className=\"w-4 h-4\" />,\r\n    },\r\n   \r\n    { href: \"/careers\", label: \"Career\", icon: <Briefcase className=\"w-4 h-4\" /> },\r\n    { href: \"/store\", label: \"Store\", icon: <ShoppingBagIcon className=\"w-4 h-4\" /> },\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <header className=\"sticky top-0 z-50 w-full bg-black overflow-x-hidden\">\r\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex h-20 items-center justify-between\">\r\n            <Link\r\n              href=\"/\"\r\n              className=\"flex items-center space-x-2 transition-transform hover:scale-105\"\r\n            >\r\n              <Image\r\n                src=\"/logo_black.png\"\r\n                alt=\"Preply Logo\"\r\n                width={150}\r\n                height={50}\r\n                className=\"rounded-sm\"\r\n              />\r\n            </Link>\r\n\r\n            <nav className=\"hidden md:flex items-center space-x-4\">\r\n              {navLinks.map((link) => (\r\n                <Link\r\n                  key={link.href}\r\n                  href={link.href}\r\n                  className=\"relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400\"\r\n                >\r\n                  {link.icon}\r\n                  {link.label}\r\n                  {link.label === \"Find School\" && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black\">\r\n                      Coming Soon\r\n                    </span>\r\n                  )}\r\n                  {link.isNew && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse\">\r\n                      Trending\r\n                    </span>\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n\r\n            {/* Mobile Notification Bell */}\r\n            <div className=\"flex md:hidden items-center space-x-2\">\r\n              {isAuthenticated && (\r\n                <NotificationBell userType=\"class\" />\r\n              )}\r\n              {isStudentLoggedIn && (\r\n                <NotificationBell userType=\"student\" />\r\n              )}\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"text-orange-400 hover:bg-orange-500/10\"\r\n                onClick={toggleMenu}\r\n              >\r\n                {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\r\n              </Button>\r\n            </div>\r\n\r\n            <div className=\"hidden md:flex items-center space-x-4\">\r\n              {isAuthenticated && (\r\n                <>\r\n                  <NotificationBell userType=\"class\" />\r\n                  <>\r\n                  <Link href=\"/coins\" passHref>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"icon\"\r\n                        className=\"relative rounded-full group bg-black h-10 w-10 border-2 border-orange-500\"\r\n                      >\r\n                        <div className=\"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                        <div className=\"relative z-10 \">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={32}\r\n                            height={32}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                      </Button>\r\n                    </Link>\r\n                </>\r\n                  <Link href=\"/classes/chat\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"icon\"\r\n                      className=\"relative rounded-full border-2 border-orange-500 group bg-black text-white hover:text-orange-400 h-10 w-10\"\r\n                    >\r\n                      <MessageSquare className=\"h-5 w-5\" />\r\n                    </Button>\r\n                  </Link>\r\n                </>\r\n              )}\r\n\r\n              <div className=\"h-8 border-l border-orange-500/20\" />\r\n\r\n              {isAuthenticated && (\r\n                <Popover>\r\n                  <PopoverTrigger asChild>\r\n                    <Avatar className=\"cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors h-10 w-10\">\r\n                      <AvatarFallback className=\"bg-white text-black flex items-center justify-center text-sm font-semibold\">\r\n                        {user?.firstName && user?.lastName\r\n                          ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                          : \"CT\"}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                  </PopoverTrigger>\r\n                  <PopoverContent className=\"w-64 bg-white\">\r\n                    <div className=\"flex items-center gap-3 mb-3 pb-2 border-b\">\r\n                      <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                        <AvatarFallback className=\"bg-white text-black\">\r\n                          {user?.firstName && user?.lastName\r\n                            ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                            : \"CT\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div>\r\n                        <p className=\"font-medium text-black\">\r\n                          {user?.firstName && user?.lastName\r\n                            ? `${user.firstName} ${user.lastName}`\r\n                            : user?.className || \"Class Account\"}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-600\">{user?.contactNo || \"<EMAIL>\"}</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-2\">\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/classes/profile\" className=\"flex items-center\">\r\n                          <User className=\"mr-2 h-4 w-4\" />\r\n                          <span>Profile</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button onClick={() => accessClassDashboard()} className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <User className=\"mr-2 h-4 w-4\" />\r\n                        <span>My Dashboard</span>\r\n                      </Button>\r\n                      {/* <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/classes/question-bank\" className=\"flex items-center\">\r\n                          <FileQuestion className=\"mr-2 h-4 w-4\" />\r\n                          <span>Question Bank</span>\r\n                        </Link>\r\n                      </Button> */}\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/classes/referral-dashboard\" className=\"flex items-center\">\r\n                          <Share2 className=\"mr-2 h-4 w-4\" />\r\n                          <span>Referral Dashboard</span>\r\n                        </Link>\r\n                      </Button>\r\n\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\"\r\n                        onClick={async () => {\r\n                          try {\r\n                            const response = await axiosInstance.post(\"/auth-client/logout\", {});\r\n                            if (response.data.success) {\r\n                              router.push(\"/\");\r\n                              dispatch(clearUser());\r\n                              localStorage.removeItem(\"token\");\r\n                              toast.success(\"Logged out successfully\");\r\n                            }\r\n                          } catch (error) {\r\n                            console.error(\"Logout error:\", error);\r\n                            toast.error(\"Failed to logout\");\r\n                          }\r\n                        }}\r\n                      >\r\n                        Logout\r\n                      </Button>\r\n                    </div>\r\n                  </PopoverContent>\r\n                </Popover>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <Button\r\n                  className=\"bg-customOrange hover:bg-[#E88143] text-white mr-4\"\r\n                  asChild\r\n                >\r\n                  <Link href=\"/class/login\">Join as a Tutor/Class</Link>\r\n                </Button>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"bg-black border-orange-500 hover:bg-orange-900/50 text-white hover:text-white\"\r\n                  asChild\r\n                >\r\n                  <Link href=\"/student/login\">Student Login</Link>\r\n                </Button>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <>\r\n                  <NotificationBell userType=\"student\" />\r\n                  <>\r\n                  <Link href=\"/coins\" passHref>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"icon\"\r\n                        className=\"relative rounded-full group bg-black h-10 w-10 border-2 border-orange-500\"\r\n                      >\r\n                        <div className=\"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                        <div className=\"relative z-10 \">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={32}\r\n                            height={32}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                      </Button>\r\n                    </Link>\r\n                </>\r\n                  <Link href=\"/student/chat\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"icon\"\r\n                      className=\"relative rounded-full group border-2 border-orange-500 bg-black text-white hover:text-orange-400 h-10 w-10\"\r\n                    >\r\n                      <MessageSquare className=\"h-5 w-5\" />\r\n                    </Button>\r\n                  </Link>\r\n                </>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <Popover>\r\n                  <PopoverTrigger asChild>\r\n                    <Avatar className=\"cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors h-10 w-10\">\r\n                      <AvatarFallback className=\"bg-white text-black flex items-center justify-center text-sm font-semibold\">\r\n                        {studentData?.firstName && studentData?.lastName\r\n                          ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                          : \"ST\"}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                  </PopoverTrigger>\r\n                  <PopoverContent className=\"w-64 bg-white\">\r\n                    <div className=\"flex items-center gap-3 mb-3 pb-2 border-b\">\r\n                      <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                        <AvatarFallback className=\"bg-white text-black\">\r\n                          {studentData?.firstName && studentData?.lastName\r\n                            ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                            : \"ST\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div>\r\n                        <p className=\"font-medium text-black\">\r\n                          {studentData?.firstName && studentData?.lastName\r\n                            ? `${studentData.firstName} ${studentData.lastName}`\r\n                            : \"Student Account\"}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-600\">{studentData?.contactNo || \"<EMAIL>\"}</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-2\">\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/student/profile\" className=\"flex items-center\">\r\n                          <UserCircle className=\"mr-2 h-4 w-4\" />\r\n                          <span>Profile</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/student/wishlist\" className=\"flex items-center\">\r\n                          <ShoppingBag className=\"mr-2 h-4 w-4\" />\r\n                          <span>My Wishlist</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/student/referral-dashboard\" className=\"flex items-center\">\r\n                          <Share2 className=\"mr-2 h-4 w-4\" />\r\n                          <span>Referral Dashboard</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/student/my-orders\" className=\"flex items-center\">\r\n                          <ShoppingBag className=\"mr-2 h-4 w-4\" />\r\n                          <span>My Orders</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\"\r\n                        onClick={handleStudentLogout}\r\n                      >\r\n                        Logout\r\n                      </Button>\r\n                    </div>\r\n                  </PopoverContent>\r\n                </Popover>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      <div>\r\n        <div\r\n          className={`fixed inset-y-0 right-0 z-50 w-72 bg-black/95 shadow-2xl transform transition-all duration-300 ease-in-out md:hidden border-l border-orange-500/20 ${\r\n            isMenuOpen ? \"translate-x-0\" : \"translate-x-full\"\r\n          }`}\r\n        >\r\n          <div className=\"flex flex-col h-full p-6\">\r\n            <div className=\"flex justify-end\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"text-orange-400 hover:bg-orange-500/10 rounded-full\"\r\n                onClick={toggleMenu}\r\n              >\r\n                <X className=\"h-6 w-6\" />\r\n              </Button>\r\n            </div>\r\n\r\n            <nav className=\"flex flex-col space-y-2 mt-8\">\r\n              {navLinks.map((link) => (\r\n                <Link\r\n                  key={link.href}\r\n                  href={link.href}\r\n                  className=\"px-4 py-3 border text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-orange-500/10 rounded-lg transition-colors flex justify-between items-center\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <div className=\"flex items-center gap-3\">\r\n                    {link.icon}\r\n                    {typeof link.label === \"string\" ? (\r\n                      <span>{link.label}</span>\r\n                    ) : (\r\n                      link.label\r\n                    )}\r\n                  </div>\r\n                  {link.label === \"Find School\" && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black animate-pulse\">\r\n                      Coming Soon\r\n                    </span>\r\n                  )}\r\n                  {link.isNew && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white\">\r\n                      New\r\n                    </span>\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n\r\n            <div className=\"mt-auto space-y-4\">\r\n              {isAuthenticated && (\r\n                <>\r\n                  <Link href=\"/classes/profile\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <User className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Profile</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                    onClick={() => accessClassDashboard()}\r\n                  >\r\n                    <div className=\"absolute inset-0\" />\r\n                    <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                      <div className=\"p-1.5 rounded-full\">\r\n                        <LayoutDashboard className=\"h-5 w-5 text-white\" />\r\n                      </div>\r\n                      <span className=\"font-medium text-gray-300\">My Dashboard</span>\r\n                    </div>\r\n                  </Button>\r\n                  {/* <Link href=\"/classes/question-bank\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <FileQuestion className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Question Bank</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link> */}\r\n                  <Link href=\"/classes/referral-dashboard\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <Share2 className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Referral Dashboard</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n                  <Link href=\"/coins\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={20}\r\n                            height={20}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">My Coins</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Link href=\"/classes/chat\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <MessageSquare className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Chat</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full border-orange-500 text-orange-500 hover:bg-orange-500/10 hover:text-white mt-3\"\r\n                    onClick={async () => {\r\n                      try {\r\n                        const response = await axiosInstance.post(\"/auth-client/logout\", {});\r\n                        if (response.data.success) {\r\n                          router.push(\"/\");\r\n                          dispatch(clearUser());\r\n                          localStorage.removeItem(\"token\");\r\n                          toast.success(\"Logged out successfully\");\r\n                        }\r\n                      } catch (error) {\r\n                        console.error(\"Logout error:\", error);\r\n                        toast.error(\"Failed to logout\");\r\n                      }\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <div className=\"flex items-center justify-center gap-3\">\r\n                      <User className=\"h-5 w-5\" />\r\n                      <span>Logout</span>\r\n                    </div>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <>\r\n                  {studentData?.firstName && studentData?.lastName && (\r\n                    <div className=\"p-3 border border-[#ff914d]/20 rounded-lg bg-white\">\r\n                      <div className=\"flex items-center gap-3\">\r\n                        <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                          <AvatarFallback className=\"bg-white text-black\">\r\n                            {(`${studentData.firstName[0]}${studentData.lastName[0]}`).toUpperCase()}\r\n                          </AvatarFallback>\r\n                        </Avatar>\r\n                        <div>\r\n                          <p className=\"font-medium text-black\">{`${studentData.firstName} ${studentData.lastName}`}</p>\r\n                          <p className=\"text-xs text-gray-600\">{studentData.email}</p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                  <Button\r\n                    asChild\r\n                    className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <Link href=\"/student/profile\" className=\"flex items-center justify-center gap-3\">\r\n                      <UserCircle className=\"h-5 w-5\" />\r\n                      <span>Profile</span>\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    asChild\r\n                    className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <Link href=\"/student/wishlist\" className=\"flex items-center justify-center gap-3\">\r\n                      <ShoppingBag className=\"h-5 w-5\" />\r\n                      <span>My Wishlist</span>\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    asChild\r\n                    className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <Link href=\"/student/referral-dashboard\" className=\"flex items-center justify-center gap-3\">\r\n                      <Share2 className=\"h-5 w-5\" />\r\n                      <span>Referral Dashboard</span>\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    asChild\r\n                    className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <Link href=\"/student/my-orders\" className=\"flex items-center justify-center gap-3\">\r\n                      <ShoppingBag className=\"h-5 w-5\" />\r\n                      <span>My Orders</span>\r\n                    </Link>\r\n                  </Button>\r\n                  <Link href=\"/coins\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-orange-500 hover:border-orange-400 bg-black mb-3\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                      <div className=\"relative z-10 flex items-center justify-center gap-3\">\r\n                        <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={20}\r\n                            height={20}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">My Coins</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Link href=\"/student/chat\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-orange-500 hover:border-orange-400 bg-black mb-3\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                      <div className=\"relative z-10 flex items-center justify-center gap-3\">\r\n                        <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                          <MessageSquare className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Chat</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\"\r\n                    onClick={() => {\r\n                      handleStudentLogout();\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <div className=\"flex items-center justify-center gap-3\">\r\n                      <User className=\"h-5 w-5\" />\r\n                      <span>Logout</span>\r\n                    </div>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <div className=\"space-y-3 pt-3\">\r\n                  <Button\r\n                    variant=\"default\"\r\n                    className=\"w-full bg-orange-500 hover:bg-orange-600\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/class/login\" onClick={toggleMenu}>\r\n                      Tutor/Classes Login\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full border-customOrange text-orange-500 hover:bg-orange\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/student/login\" onClick={toggleMenu}>\r\n                      Student Login\r\n                    </Link>\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {isStudentLoggedIn && <ProfileCompletionIndicator />}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAIA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AA/BA;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,MAAM,SAAS;IACb,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,IAAI;IAC9E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,IAAI,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,QAAQ,eAAe;IAG7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD;QACxC,qBAAqB;QAErB,IAAI,YAAY;YACd,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,eAAe,KAAK,KAAK,CAAC;YAC5B;YACA,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;QAC7B;QAEA,MAAM,sBAAsB;YAC1B,MAAM,iBAAiB,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD;YAC5C,qBAAqB;YACrB,IAAI,gBAAgB;gBAClB,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,eAAe,KAAK,KAAK,CAAC;gBAC5B;gBACA,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;YAC7B,OAAO;gBACL,eAAe;YACjB;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QAEnC,IAAI,WAAW,OAAO,EAAE;YACtB,MAAM,QAAQ,WAAW,OAAO,CAAC,qBAAqB,GAAG,KAAK;YAC9D,gBAAgB;QAClB;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qLAAA,CAAA,oBAAiB,AAAD,EAAE,CAAC,MAAM;QACvB,IAAI,iBAAiB,GAAG;QACxB,MAAM,WAAW,EAAE,GAAG;QACtB,MAAM,SAAS,AAAC,QAAQ,QAAS;QACjC,IAAI,OAAO,WAAW;QACtB,IAAI,QAAQ,CAAC,cAAc;YACzB,OAAO;QACT;QACA,EAAE,GAAG,CAAC;IACR;IAEA,MAAM,aAAa,IAAM,cAAc,CAAC;IAExC,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;YACnC,IAAI,SAAS,OAAO,KAAK,OAAO;gBAC9B,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;gBAC/B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,aAAa,CAAC,IAAI,MAAM;YACjC,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;gBAChC,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,oBAAoB;YAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,aAAa,UAAU,CAAC;YACxB,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;YACpB,qBAAqB;YACrB,eAAe;YACf,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;QACjC;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE,MAAM,WAAW,MAAM;YAE1D,IAAI,SAAS,OAAO,EAAE;gBACpB,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI;gBAC/B,MAAM,cAAc,6DAAwC,sBAAsB,EAAE,MAAM,GAAG,OAAO,EAAE,OAAO;gBAC7G,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAqB,OAAO;YAAc,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;QAAa;QAC9F;YAAE,MAAM;YAAU,OAAO;YAAY,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAAc,OAAO;QAAK;QACtF;YACE,MAAM;YACN,qBACE,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;kCAAK;;;;;;oBACL,mCAAqB,8OAAC,8IAAA,CAAA,UAAa;wBAAC,WAAW,aAAa;;;;;;;;;;;;YAGjE,oBAAM,8OAAC,gNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC7B;QAEA;YAAE,MAAM;YAAY,OAAO;YAAU,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAAa;QAC7E;YAAE,MAAM;YAAU,OAAO;YAAS,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;QAAa;KACjF;IAED,qBACE;;0BACE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAId,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;4CAET,KAAK,IAAI;4CACT,KAAK,KAAK;4CACV,KAAK,KAAK,KAAK,+BACd,8OAAC;gDAAK,WAAU;0DAAiE;;;;;;4CAIlF,KAAK,KAAK,kBACT,8OAAC;gDAAK,WAAU;0DAA+E;;;;;;;uCAZ5F,KAAK,IAAI;;;;;;;;;;0CAqBpB,8OAAC;gCAAI,WAAU;;oCACZ,iCACC,8OAAC,6IAAA,CAAA,UAAgB;wCAAC,UAAS;;;;;;oCAE5B,mCACC,8OAAC,6IAAA,CAAA,UAAgB;wCAAC,UAAS;;;;;;kDAE7B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;kDAER,2BAAa,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAAe,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAI9D,8OAAC;gCAAI,WAAU;;oCACZ,iCACC;;0DACE,8OAAC,6IAAA,CAAA,UAAgB;gDAAC,UAAS;;;;;;0DAC3B;0DACA,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,QAAQ;8DACxB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;;0EAEV,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oEACJ,KAAI;oEACJ,KAAI;oEACJ,OAAO;oEACP,QAAQ;oEACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAMpB,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgB,QAAQ;0DACjC,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DAEV,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;;;;;;;;kDAMjC,8OAAC;wCAAI,WAAU;;;;;;oCAEd,iCACC,8OAAC,mIAAA,CAAA,UAAO;;0DACN,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;8DAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB,MAAM,aAAa,MAAM,WACtB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD;;;;;;;;;;;;;;;;0DAIV,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,WAAU;;kEACxB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,WAAU;0EAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;oEAAC,WAAU;8EACvB,MAAM,aAAa,MAAM,WACtB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD;;;;;;;;;;;0EAGR,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFACV,MAAM,aAAa,MAAM,WACtB,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,MAAM,aAAa;;;;;;kFAEzB,8OAAC;wEAAE,WAAU;kFAAyB,MAAM,aAAa;;;;;;;;;;;;;;;;;;kEAI7D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO;gEAAC,WAAU;0EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAmB,WAAU;;sFACtC,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;sFAAK;;;;;;;;;;;;;;;;;0EAGV,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAS,IAAM;gEAAwB,WAAU;;kFACvD,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,8OAAC;kFAAK;;;;;;;;;;;;0EAQR,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO;gEAAC,WAAU;0EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAK;oEAA8B,WAAU;;sFACjD,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,8OAAC;sFAAK;;;;;;;;;;;;;;;;;0EAIV,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,WAAU;gEACV,SAAS;oEACP,IAAI;wEACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC;wEAClE,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;4EACzB,OAAO,IAAI,CAAC;4EACZ,SAAS,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD;4EACjB,aAAa,UAAU,CAAC;4EACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wEAChB;oEACF,EAAE,OAAO,OAAO;wEACd,QAAQ,KAAK,CAAC,iBAAiB;wEAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oEACd;gEACF;0EACD;;;;;;;;;;;;;;;;;;;;;;;;oCAQR,CAAC,mBAAmB,CAAC,mCACpB,8OAAC,kIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,OAAO;kDAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAe;;;;;;;;;;;oCAI7B,CAAC,mBAAmB,CAAC,mCACpB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,OAAO;kDAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAiB;;;;;;;;;;;oCAI/B,mCACC;;0DACE,8OAAC,6IAAA,CAAA,UAAgB;gDAAC,UAAS;;;;;;0DAC3B;0DACA,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,QAAQ;8DACxB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;;0EAEV,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oEACJ,KAAI;oEACJ,KAAI;oEACJ,OAAO;oEACP,QAAQ;oEACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAMpB,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgB,QAAQ;0DACjC,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DAEV,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;;;;;;;;oCAMhC,mCACC,8OAAC,mIAAA,CAAA,UAAO;;0DACN,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;8DAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB,aAAa,aAAa,aAAa,WACpC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;;;;;;0DAIV,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,WAAU;;kEACxB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,WAAU;0EAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;oEAAC,WAAU;8EACvB,aAAa,aAAa,aAAa,WACpC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;0EAGR,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFACV,aAAa,aAAa,aAAa,WACpC,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE,GAClD;;;;;;kFAEN,8OAAC;wEAAE,WAAU;kFAAyB,aAAa,aAAa;;;;;;;;;;;;;;;;;;kEAIpE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO;gEAAC,WAAU;0EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAmB,WAAU;;sFACtC,8OAAC,kNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;sFACtB,8OAAC;sFAAK;;;;;;;;;;;;;;;;;0EAGV,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO;gEAAC,WAAU;0EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAoB,WAAU;;sFACvC,8OAAC,oNAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,8OAAC;sFAAK;;;;;;;;;;;;;;;;;0EAGV,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO;gEAAC,WAAU;0EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAK;oEAA8B,WAAU;;sFACjD,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,8OAAC;sFAAK;;;;;;;;;;;;;;;;;0EAGV,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO;gEAAC,WAAU;0EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAqB,WAAU;;sFACxC,8OAAC,oNAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,8OAAC;sFAAK;;;;;;;;;;;;;;;;;0EAGV,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,WAAU;gEACV,SAAS;0EACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYjB,8OAAC;;kCACC,8OAAC;wBACC,WAAW,CAAC,mJAAmJ,EAC7J,aAAa,kBAAkB,oBAC/B;kCAEF,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;kDAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS;;8DAET,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,IAAI;wDACT,OAAO,KAAK,KAAK,KAAK,yBACrB,8OAAC;sEAAM,KAAK,KAAK;;;;;mEAEjB,KAAK,KAAK;;;;;;;gDAGb,KAAK,KAAK,KAAK,+BACd,8OAAC;oDAAK,WAAU;8DAA+E;;;;;;gDAIhG,KAAK,KAAK,kBACT,8OAAC;oDAAK,WAAU;8DAAiE;;;;;;;2CAnB9E,KAAK,IAAI;;;;;;;;;;8CA2BpB,8OAAC;oCAAI,WAAU;;wCACZ,iCACC;;8DACE,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAmB,QAAQ;8DACpC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAIlD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS,IAAM;;sEAEf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,4NAAA,CAAA,kBAAe;wEAAC,WAAU;;;;;;;;;;;8EAE7B,8OAAC;oEAAK,WAAU;8EAA4B;;;;;;;;;;;;;;;;;;8DAkBhD,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA8B,QAAQ;8DAC/C,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;kFAEpB,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAIlD,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,QAAQ;8DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4EACJ,KAAI;4EACJ,KAAI;4EACJ,OAAO;4EACP,QAAQ;4EACR,WAAU;;;;;;;;;;;kFAGd,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,QAAQ;8DACjC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;;;;;;;kFAE3B,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;wDACP,IAAI;4DACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC;4DAClE,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gEACzB,OAAO,IAAI,CAAC;gEACZ,SAAS,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD;gEACjB,aAAa,UAAU,CAAC;gEACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4DAChB;wDACF,EAAE,OAAO,OAAO;4DACd,QAAQ,KAAK,CAAC,iBAAiB;4DAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wDACd;wDACA;oDACF;8DAEA,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;wCAMb,mCACC;;gDACG,aAAa,aAAa,aAAa,0BACtC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,WAAU;0EAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;oEAAC,WAAU;8EACvB,AAAC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAE,WAAW;;;;;;;;;;;0EAG1E,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAA0B,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE;;;;;;kFACzF,8OAAC;wEAAE,WAAU;kFAAyB,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;8DAK/D,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAmB,WAAU;;0EACtC,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAoB,WAAU;;0EACvC,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAA8B,WAAU;;0EACjD,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAqB,WAAU;;0EACxC,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,QAAQ;8DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4EACJ,KAAI;4EACJ,KAAI;4EACJ,OAAO;4EACP,QAAQ;4EACR,WAAU;;;;;;;;;;;kFAGd,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,QAAQ;8DACjC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;;;;;;;kFAE3B,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;wDACP;wDACA;oDACF;8DAEA,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;wCAMb,CAAC,mBAAmB,CAAC,mCACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAe,SAAS;kEAAY;;;;;;;;;;;8DAIjD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAiB,SAAS;kEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU5D,mCAAqB,8OAAC,uJAAA,CAAA,UAA0B;;;;;;;;;;;;;AAIzD;uCAEe", "debugId": null}}, {"offset": {"line": 3833, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/separator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SeparatorPrimitive from '@radix-ui/react-separator';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = 'horizontal',\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        'bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Separator };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3863, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/progress.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as ProgressPrimitive from '@radix-ui/react-progress';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Progress({\r\n  className,\r\n  value,\r\n  ...props\r\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\r\n  return (\r\n    <ProgressPrimitive.Root\r\n      data-slot=\"progress\"\r\n      className={cn('bg-primary/20 relative h-2 w-full overflow-hidden rounded-full', className)}\r\n      {...props}\r\n    >\r\n      <ProgressPrimitive.Indicator\r\n        data-slot=\"progress-indicator\"\r\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\r\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n      />\r\n    </ProgressPrimitive.Root>\r\n  );\r\n}\r\n\r\nexport { Progress };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 3902, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Footer.tsx"], "sourcesContent": ["'use client';\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport {\r\n  FaFacebookF,\r\n  FaTwitter,\r\n  FaInstagram,\r\n  FaLinkedinIn,\r\n  FaTumblr,\r\n  FaPinterestP,\r\n  FaEnvelope,\r\n} from 'react-icons/fa';\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <footer className=\"bg-black text-gray-300 px-6 py-16\">\r\n      <div className=\"container mx-auto max-w-7xl space-y-16\">\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between gap-6\">\r\n          <Link href=\"/\" className=\"flex items-center gap-2\">\r\n            <Image\r\n              src=\"/logo_black.png\"\r\n              alt=\"Logo\"\r\n              width={200}\r\n              height={40}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n\r\n          <div className=\"flex flex-wrap justify-center gap-1\">\r\n            {[\r\n              { href: 'mailto:<EMAIL>', icon: FaEnvelope, label: 'Email Us' },\r\n              {\r\n                href: 'https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09',\r\n                icon: FaTwitter,\r\n                label: 'Twitter',\r\n              },\r\n              {\r\n                href: 'https://www.facebook.com/share/1FNYcyqawH/',\r\n                icon: FaFacebookF,\r\n                label: 'Facebook',\r\n              },\r\n              {\r\n                href: 'https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==',\r\n                icon: FaInstagram,\r\n                label: 'Instagram',\r\n              },\r\n              {\r\n                href: 'https://www.linkedin.com/company/uest-edtech/',\r\n                icon: FaLinkedinIn,\r\n                label: 'LinkedIn',\r\n              },\r\n              { href: 'https://pin.it/1Di0EFtAa', icon: FaPinterestP, label: 'Pinterest' },\r\n              {\r\n                href: 'https://www.tumblr.com/uestedtech?source=share',\r\n                icon: FaTumblr,\r\n                label: 'Tumblr',\r\n              },\r\n            ].map(({ href, icon: Icon, label }) => (\r\n              <div key={label} className=\"flex flex-col items-center\">\r\n                <Link\r\n                  href={href}\r\n                  className=\"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition\"\r\n                  title={label}\r\n                >\r\n                  <Icon className=\"text-xl text-white hover:text-gray-400 transition\" />\r\n                </Link>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10\">\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">About</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Tutors\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/support\" className=\"hover:text-white transition\">\r\n                  Support\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/careers\" className=\"hover:text-white transition\">\r\n                  Careers\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">For Students</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/student/login\" className=\"hover:text-white transition\">\r\n                  Student Login\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Online Tutor\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/uwhiz\" className=\"hover:text-white transition\">\r\n                  Uwhiz\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Contact</h3>\r\n            <address className=\"not-italic text-sm space-y-1 leading-relaxed\">\r\n              <p>Head Office</p>\r\n              <p>4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641</p>\r\n              <p>Contact: +91 96 877 877 88</p>\r\n              <p>Email: <EMAIL></p>\r\n            </address>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Apps</h3>\r\n           <Link href=\"https://play.google.com/store/apps/details?id=com.uest\" target=\"_blank\">\r\n            <Image\r\n              src=\"/playstore.png\"\r\n              alt=\"Google Play Store\"\r\n              width={180}\r\n              height={50}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom Bar */}\r\n        <div className=\"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4\">\r\n          <p>© 2025 uest.in. All rights reserved.</p>\r\n          <div className=\"flex gap-4\">\r\n            <Link href=\"/terms-and-conditions\" className=\"hover:text-white transition\">\r\n              Terms & Conditions\r\n            </Link>\r\n            <Link href=\"/privacy-policy\" className=\"hover:text-white transition\">\r\n              Privacy Policy\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAcA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,MAAM;oCAA8B,MAAM,8IAAA,CAAA,aAAU;oCAAE,OAAO;gCAAW;gCAC1E;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,YAAS;oCACf,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,eAAY;oCAClB,OAAO;gCACT;gCACA;oCAAE,MAAM;oCAA4B,MAAM,8IAAA,CAAA,eAAY;oCAAE,OAAO;gCAAY;gCAC3E;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,WAAQ;oCACd,OAAO;gCACT;6BACD,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,iBAChC,8OAAC;oCAAgB,WAAU;8CACzB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM;wCACN,WAAU;wCACV,OAAO;kDAEP,cAAA,8OAAC;4CAAK,WAAU;;;;;;;;;;;mCANV;;;;;;;;;;;;;;;;8BAahB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;sDAIhE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAOpE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAiB,WAAU;0DAA8B;;;;;;;;;;;sDAItE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAOlE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAQ,WAAU;;sDACjB,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAIP,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACvD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAyD,QAAO;8CAC1E,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOhB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAE;;;;;;sCACH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAwB,WAAU;8CAA8B;;;;;;8CAG3E,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAkB,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF;uCAEe", "debugId": null}}, {"offset": {"line": 4313, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/student/profile/components/sidebar-nav.tsx"], "sourcesContent": ["import { useSelector } from 'react-redux';\r\nimport { RootState } from '@/store';\r\nimport { CheckCircle } from 'lucide-react';\r\n\r\ninterface SidebarNavProps {\r\n  items: { title: string; href: string }[];\r\n  activeSection: string;\r\n  setActiveSection: (section: string) => void;\r\n}\r\n\r\nexport function SidebarNav({ items, activeSection, setActiveSection }: SidebarNavProps) {\r\n  const { profileData } = useSelector((state: RootState) => state.studentProfile);\r\n\r\n  const isFormCompleted = (title: string) => {\r\n    if (!profileData?.profile) return false;\r\n    \r\n    const profile = profileData.profile;\r\n    \r\n    switch (title) {\r\n      case \"Personal Info\":\r\n        return !!(profile.student?.firstName && profile.student?.lastName && \r\n                 profile.student?.contact && profile.birthday && profile.school && profile.address);\r\n      case \"Educational Info\":\r\n        return !!(profile.medium && profile.classroom);\r\n      case \"Documents & Photo\":\r\n        return !!(profile.photo && profile.documentUrl);\r\n      default:\r\n        return false;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <nav className=\"space-y-1\">\r\n      {items.map((item, index) => {\r\n        const isActive = activeSection === item.href.replace('#', '');\r\n        const isCompleted = isFormCompleted(item.title);\r\n        \r\n        // Disable if previous form is not completed (except for first item)\r\n        const isDisabled = index > 0 && !isFormCompleted(items[index - 1].title);\r\n\r\n        return (\r\n          <button\r\n            key={item.href}\r\n            onClick={() => !isDisabled && setActiveSection(item.href.replace('#', ''))}\r\n            className={`flex items-center w-[200px] justify-between rounded-md px-3 py-2 text-sm font-medium transition-colors ${\r\n              isActive \r\n                ? 'bg-muted text-primary' \r\n                : isDisabled \r\n                ? 'text-gray-400 cursor-not-allowed' \r\n                : 'text-muted-foreground hover:text-primary'\r\n            }`}\r\n            disabled={isDisabled}\r\n          >\r\n            <span>{item.title}</span>\r\n            {isCompleted && <CheckCircle size={16} className=\"text-green-500\" />}\r\n          </button>\r\n        );\r\n      })}\r\n    </nav>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAQO,SAAS,WAAW,EAAE,KAAK,EAAE,aAAa,EAAE,gBAAgB,EAAmB;IACpF,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,cAAc;IAE9E,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,aAAa,SAAS,OAAO;QAElC,MAAM,UAAU,YAAY,OAAO;QAEnC,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,CAAC,CAAC,QAAQ,OAAO,EAAE,aAAa,QAAQ,OAAO,EAAE,YAChD,QAAQ,OAAO,EAAE,WAAW,QAAQ,QAAQ,IAAI,QAAQ,MAAM,IAAI,QAAQ,OAAO;YAC5F,KAAK;gBACH,OAAO,CAAC,CAAC,CAAC,QAAQ,MAAM,IAAI,QAAQ,SAAS;YAC/C,KAAK;gBACH,OAAO,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,QAAQ,WAAW;YAChD;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM;YAChB,MAAM,WAAW,kBAAkB,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK;YAC1D,MAAM,cAAc,gBAAgB,KAAK,KAAK;YAE9C,oEAAoE;YACpE,MAAM,aAAa,QAAQ,KAAK,CAAC,gBAAgB,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK;YAEvE,qBACE,8OAAC;gBAEC,SAAS,IAAM,CAAC,cAAc,iBAAiB,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK;gBACtE,WAAW,CAAC,uGAAuG,EACjH,WACI,0BACA,aACA,qCACA,4CACJ;gBACF,UAAU;;kCAEV,8OAAC;kCAAM,KAAK,KAAK;;;;;;oBAChB,6BAAe,8OAAC,2NAAA,CAAA,cAAW;wBAAC,MAAM;wBAAI,WAAU;;;;;;;eAZ5C,KAAK,IAAI;;;;;QAepB;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 4384, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/student/profile/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useRef, useEffect, Suspense } from 'react';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport { useForm } from 'react-hook-form';\r\nimport { z } from 'zod';\r\nimport { toast } from 'sonner';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport { format } from 'date-fns';\r\nimport { Calendar as CalendarIcon, FileText, X, Camera, Upload, Check } from 'lucide-react';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { RootState, AppDispatch } from '@/store';\r\nimport { fetchStudentProfile, updateStudentProfile } from '@/store/thunks/studentProfileThunks';\r\nimport { updateProfilePhoto } from '@/store/slices/studentProfileSlice';\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from '@/components/ui/form';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { Calendar } from '@/components/ui/calendar';\r\nimport { cn } from '@/lib/utils';\r\nimport Image from 'next/image';\r\nimport Header from '../../../app-components/Header';\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { Progress } from \"@/components/ui/progress\";\r\nimport Footer from \"@/app-components/Footer\";\r\nimport { SidebarNav } from \"./components/sidebar-nav\";\r\n\r\nconst profileFormSchema = z.object({\r\n  firstName: z.string().min(2, 'First name must be at least 2 characters.'),\r\n  middleName: z.string().optional(),\r\n  lastName: z.string().min(2, 'Last name must be at least 2 characters.'),\r\n  mothersName: z.string().optional(),\r\n  email: z.string().email('Please enter a valid email address.').optional().or(z.literal('')),\r\n  contact: z\r\n    .string()\r\n    .min(10, 'Contact number must be at least 10 digits.')\r\n    .max(15, 'Contact number must not exceed 15 digits.')\r\n    .regex(/^\\d+$/, 'Contact number must contain only digits.'),\r\n  contact2: z\r\n    .string()\r\n    .min(10, 'Contact number must be at least 10 digits.')\r\n    .max(15, 'Contact number must not exceed 15 digits.')\r\n    .regex(/^\\d+$/, 'Contact number must contain only digits.')\r\n    .optional()\r\n    .or(z.literal('')),\r\n  medium: z.string().min(1, 'Medium of instruction is required'),\r\n  classroom: z.string().min(1, 'Standard is required'),\r\n  gender: z.string().optional(),\r\n  birthday: z.date({ required_error: 'Please select your date of birth' }),\r\n  school: z.string().min(2, 'School name must be at least 2 characters.'),\r\n  address: z.string().min(5, 'Address must be at least 5 characters.'),\r\n  age: z.string().optional(),\r\n  aadhaarNumber: z\r\n    .string()\r\n    .min(12, 'Aadhaar number must be 12 digits.')\r\n    .max(12, 'Aadhaar number must be 12 digits.')\r\n    .regex(/^\\d+$/, 'Aadhaar number must contain only digits.')\r\n    .optional()\r\n    .or(z.literal('')),\r\n  bloodGroup: z.string().optional(),\r\n  birthPlace: z.string().optional(),\r\n  motherTongue: z.string().optional(),\r\n  religion: z.string().optional(),\r\n  caste: z.string().optional(),\r\n  subCaste: z.string().optional(),\r\n  photo: z.any().optional(),\r\n  document: z.any().optional(),\r\n});\r\n\r\ntype ProfileFormValues = z.infer<typeof profileFormSchema>;\r\n\r\nconst sidebarNavItems = [\r\n  {\r\n    title: \"Personal Info\",\r\n    href: \"#personal-info\",\r\n  },\r\n  {\r\n    title: \"Other Info\",\r\n    href: \"#other-info\",\r\n  },\r\n];\r\n\r\nconst StudentProfileContent = () => {\r\n  const router = useRouter();\r\n  const dispatch = useDispatch<AppDispatch>();\r\n  const searchParams = useSearchParams();\r\n  const fromQuiz = searchParams.get('quiz') === 'true';\r\n  const examId = searchParams.get('examId');\r\n\r\n  const [activeSection, setActiveSection] = useState('personal-info');\r\n  const [photo, setPhoto] = useState<string | null>(null);\r\n  const [isCameraOpen, setIsCameraOpen] = useState(false);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [cameraError, setCameraError] = useState<string | null>(null);\r\n  const [uploadedDocument, setUploadedDocument] = useState<\r\n    File | { name: string; size: number; url: string; type: string } | null\r\n  >(null);\r\n  const [isDocumentRemoved, setIsDocumentRemoved] = useState(false);\r\n  const [progress, setProgress] = useState(0);\r\n\r\n  const { profileData, loading: profileLoading } = useSelector(\r\n    (state: RootState) => state.studentProfile\r\n  );\r\n\r\n  const profile = profileData?.profile || null;\r\n  const classroomOptions = profileData?.classroomOptions || [];\r\n\r\n  const videoRef = useRef<HTMLVideoElement>(null);\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n\r\n  const form = useForm<ProfileFormValues>({\r\n    resolver: zodResolver(profileFormSchema),\r\n    defaultValues: {\r\n      firstName: '',\r\n      middleName: '',\r\n      lastName: '',\r\n      mothersName: '',\r\n      email: '',\r\n      contact: '',\r\n      contact2: '',\r\n      medium: '',\r\n      classroom: '',\r\n      gender: '',\r\n      birthday: undefined,\r\n      school: '',\r\n      address: '',\r\n      age: '',\r\n      aadhaarNumber: '',\r\n      bloodGroup: '',\r\n      birthPlace: '',\r\n      motherTongue: '',\r\n      religion: '',\r\n      caste: '',\r\n      subCaste: '',\r\n    },\r\n    mode: 'onSubmit',\r\n  });\r\n\r\n  useEffect(() => {\r\n    const studentToken = localStorage.getItem('studentToken');\r\n    if (!studentToken) {\r\n      toast.error('Please login to access your profile');\r\n      router.push('/');\r\n    }\r\n  }, [router]);\r\n\r\n  useEffect(() => {\r\n    const studentToken = localStorage.getItem('studentToken');\r\n    if (studentToken) {\r\n      dispatch(fetchStudentProfile());\r\n    }\r\n  }, [dispatch]);\r\n\r\n  useEffect(() => {\r\n    if (profileData?.profile) {\r\n      const profile = profileData.profile;\r\n      let completedSections = 0;\r\n      const totalSections = 3;\r\n\r\n      if (profile.student?.firstName && profile.student?.lastName && profile.student?.contact &&\r\n          profile.birthday && profile.school && profile.address) {\r\n        completedSections++;\r\n      }\r\n\r\n      if (profile.medium && profile.classroom) {\r\n        completedSections++;\r\n      }\r\n\r\n      if (profile.photo && profile.documentUrl) {\r\n        completedSections++;\r\n      }\r\n\r\n      setProgress((completedSections / totalSections) * 100);\r\n    }\r\n  }, [profileData]);\r\n\r\n  useEffect(() => {\r\n    if (!profileData) return;\r\n\r\n    const profileObj = profileData.profile;\r\n    const studentData = profileObj?.student || JSON.parse(localStorage.getItem('student_data') || '{}');\r\n\r\n    const formValues = {\r\n      firstName: studentData?.firstName || '',\r\n      middleName: studentData?.middleName || '',\r\n      lastName: studentData?.lastName || '',\r\n      mothersName: studentData?.mothersName || '',\r\n      email: studentData?.email || '',\r\n      contact: studentData?.contact || '',\r\n      contact2: profileObj?.contactNo2 || '',\r\n      medium: profileObj?.medium || '',\r\n      classroom: profileObj?.classroom || '',\r\n      gender: profileObj?.gender || '',\r\n      birthday: profileObj?.birthday ? new Date(profileObj.birthday) : undefined,\r\n      school: profileObj?.school || '',\r\n      address: profileObj?.address || '',\r\n      age: profileObj?.age?.toString() || '',\r\n      aadhaarNumber: profileObj?.aadhaarNo || '',\r\n      bloodGroup: profileObj?.bloodGroup || '',\r\n      birthPlace: profileObj?.birthPlace || '',\r\n      motherTongue: profileObj?.motherTongue || '',\r\n      religion: profileObj?.religion || '',\r\n      caste: profileObj?.caste || '',\r\n      subCaste: profileObj?.subCaste || '',\r\n    };\r\n\r\n    if (profileObj?.photo && !photo) {\r\n      setPhoto(profileObj.photo);\r\n      form.setValue('photo', profileObj.photo);\r\n    }\r\n\r\n    if (profileObj?.documentUrl && !uploadedDocument && !isDocumentRemoved) {\r\n      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/';\r\n      const documentUrl = profileObj.documentUrl.startsWith('http')\r\n        ? profileObj.documentUrl\r\n        : `${baseUrl}${profileObj.documentUrl}`;\r\n\r\n      const documentObj = {\r\n        name: documentUrl.split('/').pop() || 'Uploaded Document',\r\n        size: 0,\r\n        url: documentUrl,\r\n        type: 'application/octet-stream',\r\n      };\r\n\r\n      setUploadedDocument(documentObj);\r\n      form.setValue('document', documentObj);\r\n    }\r\n\r\n    const currentValues = form.getValues();\r\n    const isFormEmpty = !currentValues.firstName && !currentValues.lastName && !currentValues.contact;\r\n    const isEducationalDataMissing = !currentValues.medium || !currentValues.classroom;\r\n\r\n    if (isFormEmpty || isEducationalDataMissing) {\r\n      form.reset(formValues);\r\n    }\r\n  }, [profileData, form, photo, uploadedDocument, isDocumentRemoved]);\r\n\r\n  const openCamera = async () => {\r\n    setCameraError(null);\r\n\r\n    try {\r\n      if (!navigator.mediaDevices?.getUserMedia) {\r\n        throw new Error('Camera not supported on this device');\r\n      }\r\n\r\n      setIsCameraOpen(true);\r\n\r\n      const stream = await navigator.mediaDevices.getUserMedia({\r\n        video: { facingMode: 'user' },\r\n      });\r\n\r\n      if (videoRef.current) {\r\n        videoRef.current.srcObject = stream;\r\n        videoRef.current.onloadedmetadata = () => {\r\n          videoRef.current?.play().catch(() => toast.error('Error starting camera preview'));\r\n        };\r\n      }\r\n    } catch (error: any) {\r\n      setIsCameraOpen(false);\r\n      const message = error.name === 'NotAllowedError'\r\n        ? 'Please allow camera access in your browser settings.'\r\n        : 'Could not access camera. Please check your camera settings.';\r\n      setCameraError(message);\r\n      toast.error(message);\r\n    }\r\n  };\r\n\r\n  const compressImage = (canvas: HTMLCanvasElement, maxWidth: number = 800, quality: number = 0.6): string => {\r\n    const context = canvas.getContext('2d');\r\n    if (!context) return '';\r\n\r\n    const originalWidth = canvas.width;\r\n    const originalHeight = canvas.height;\r\n\r\n    let newWidth = originalWidth;\r\n    let newHeight = originalHeight;\r\n\r\n    if (originalWidth > maxWidth) {\r\n      newWidth = maxWidth;\r\n      newHeight = (originalHeight * maxWidth) / originalWidth;\r\n    }\r\n\r\n    const compressedCanvas = document.createElement('canvas');\r\n    compressedCanvas.width = newWidth;\r\n    compressedCanvas.height = newHeight;\r\n\r\n    const compressedContext = compressedCanvas.getContext('2d');\r\n    if (!compressedContext) return '';\r\n\r\n    compressedContext.drawImage(canvas, 0, 0, newWidth, newHeight);\r\n\r\n    return compressedCanvas.toDataURL('image/jpeg', quality);\r\n  };\r\n\r\n  const capturePhoto = () => {\r\n    if (!videoRef.current || !canvasRef.current) return;\r\n\r\n    const video = videoRef.current;\r\n    const canvas = canvasRef.current;\r\n    const context = canvas.getContext('2d');\r\n\r\n    canvas.width = video.videoWidth;\r\n    canvas.height = video.videoHeight;\r\n\r\n    context?.clearRect(0, 0, canvas.width, canvas.height);\r\n    context?.save();\r\n    context?.scale(-1, 1);\r\n    context?.drawImage(video, -canvas.width, 0, canvas.width, canvas.height);\r\n    context?.restore();\r\n\r\n    const compressedPhotoDataUrl = compressImage(canvas, 800, 0.6);\r\n    const base64Data = compressedPhotoDataUrl.split(',')[1];\r\n    const sizeInKB = (base64Data.length * 3) / 4 / 1024;\r\n\r\n    if (sizeInKB > 5120) {\r\n      toast.error('Photo size exceeds 5MB limit. Please try again.');\r\n      return;\r\n    }\r\n\r\n    setPhoto(compressedPhotoDataUrl);\r\n    form.setValue('photo', compressedPhotoDataUrl);\r\n    dispatch(updateProfilePhoto(compressedPhotoDataUrl));\r\n\r\n    closeCamera();\r\n  };\r\n\r\n  const closeCamera = () => {\r\n    if (videoRef.current?.srcObject) {\r\n      const stream = videoRef.current.srcObject as MediaStream;\r\n      stream.getTracks().forEach(track => track.stop());\r\n      videoRef.current.srcObject = null;\r\n    }\r\n    setIsCameraOpen(false);\r\n    setCameraError(null);\r\n  };\r\n\r\n  const removeDocument = () => {\r\n    if (uploadedDocument && 'url' in uploadedDocument && uploadedDocument.url.startsWith('blob:')) {\r\n      URL.revokeObjectURL(uploadedDocument.url);\r\n    }\r\n\r\n    setUploadedDocument(null);\r\n    setIsDocumentRemoved(true);\r\n    const fileInput = document.getElementById('document') as HTMLInputElement;\r\n    if (fileInput) fileInput.value = '';\r\n    form.setValue('document', null);\r\n  };\r\n\r\n  const formatFileSize = (bytes: number) => {\r\n    if (bytes < 1024) return bytes + ' bytes';\r\n    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';\r\n    else return (bytes / 1048576).toFixed(1) + ' MB';\r\n  };\r\n\r\n\r\n\r\n  const onSubmit = async (data: ProfileFormValues) => {\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      const currentPhoto = photo || profileData?.profile?.photo;\r\n      if (!currentPhoto) {\r\n        toast.error('Please capture a photo for your profile');\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      if (!uploadedDocument || isDocumentRemoved) {\r\n        toast.error('Identity document is required. Please upload a document.');\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      if (!(await form.trigger())) {\r\n        toast.error('Please fill in all required fields correctly');\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      const jsonData: any = {\r\n        firstName: data.firstName,\r\n        middleName: data.middleName,\r\n        lastName: data.lastName,\r\n        mothersName: data.mothersName,\r\n        email: data.email,\r\n        contact: data.contact,\r\n        contact2: data.contact2,\r\n        medium: data.medium,\r\n        classroom: data.classroom,\r\n        gender: data.gender,\r\n        birthday: data.birthday?.toISOString() || '',\r\n        school: data.school,\r\n        address: data.address,\r\n        age: data.age,\r\n        aadhaarNumber: data.aadhaarNumber,\r\n        bloodGroup: data.bloodGroup,\r\n        birthPlace: data.birthPlace,\r\n        motherTongue: data.motherTongue,\r\n        religion: data.religion,\r\n        caste: data.caste,\r\n        subCaste: data.subCaste,\r\n      };\r\n\r\n      if (photo?.startsWith('data:')) {\r\n        const base64Data = photo.split(',')[1];\r\n        const sizeInKB = (base64Data.length * 3) / 4 / 1024;\r\n\r\n        if (sizeInKB > 5120) {\r\n          toast.error('Photo size exceeds 5MB limit.');\r\n          return;\r\n        }\r\n\r\n        jsonData.photo = base64Data;\r\n        jsonData.photoMimeType = 'image/jpeg';\r\n      }\r\n\r\n      if (uploadedDocument instanceof File || (uploadedDocument && 'url' in uploadedDocument && uploadedDocument.url.startsWith('blob:'))) {\r\n        const documentFile = uploadedDocument instanceof File\r\n          ? uploadedDocument\r\n          : await fetch(uploadedDocument.url)\r\n              .then(res => res.blob())\r\n              .then(blob => new File([blob], uploadedDocument.name, { type: uploadedDocument.type }));\r\n\r\n        const documentBase64 = await new Promise<string>((resolve, reject) => {\r\n          const reader = new FileReader();\r\n          reader.onload = () => resolve((reader.result as string).split(',')[1]);\r\n          reader.onerror = reject;\r\n          reader.readAsDataURL(documentFile);\r\n        });\r\n\r\n        const docSizeKB = (documentBase64.length * 3) / 4 / 1024;\r\n        if (docSizeKB > 5120) {\r\n          toast.error('Document size exceeds 5MB limit.');\r\n          return;\r\n        }\r\n\r\n        jsonData.document = documentBase64;\r\n        jsonData.documentMimeType = documentFile.type;\r\n        jsonData.documentName = documentFile.name;\r\n      }\r\n\r\n      // Handle document removal\r\n      if (isDocumentRemoved && profileData?.profile?.documentUrl) {\r\n        jsonData.removeDocument = true;\r\n      }\r\n\r\n      const studentToken = localStorage.getItem('studentToken');\r\n      if (!studentToken) {\r\n        toast.error('Please login to submit your profile');\r\n        router.push('/');\r\n        return;\r\n      }\r\n\r\n      const result = await dispatch(updateStudentProfile(jsonData));\r\n\r\n      if (result.meta.requestStatus === 'fulfilled') {\r\n        toast.success(`Profile ${profile ? 'updated' : 'created'} successfully!`);\r\n\r\n        const existingStudentData = JSON.parse(localStorage.getItem('student_data') || '{}');\r\n        const studentData = {\r\n          ...existingStudentData,\r\n          id: existingStudentData.id || profileData?.profile?.student?.id || '',\r\n          firstName: data.firstName,\r\n          middleName: data.middleName,\r\n          lastName: data.lastName,\r\n          mothersName: data.mothersName,\r\n          email: data.email || existingStudentData.email || profileData?.profile?.student?.email || '',\r\n          contact: data.contact,\r\n        };\r\n\r\n        localStorage.setItem('student_data', JSON.stringify(studentData));\r\n        setIsDocumentRemoved(false);\r\n        await dispatch(fetchStudentProfile());\r\n\r\n        if (fromQuiz) {\r\n          if (examId) {\r\n            router.push(`/uwhiz-exam/${examId}`);\r\n          } else {\r\n            router.push('/mock-test');\r\n          }\r\n        }\r\n      } else if (result.meta.requestStatus === 'rejected') {\r\n        const errorMessage = result.payload as string;\r\n\r\n        if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {\r\n          toast.error('Your session has expired. Please login again.');\r\n          localStorage.removeItem('studentToken');\r\n          router.push('/');\r\n        } else {\r\n          toast.error(errorMessage || 'Failed to update profile');\r\n        }\r\n      }\r\n    } catch {\r\n      toast.error('Failed to submit profile information');\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Header />\r\n      <div className=\"space-y-6 p-10 pb-4 md:block\">\r\n        <div className=\"space-y-0.5\">\r\n          <h2 className=\"text-2xl font-bold tracking-tight\">\r\n            Student Profile\r\n          </h2>\r\n          <p className=\"text-muted-foreground\">\r\n            Complete your profile information. Your progress will be automatically saved as you complete each section.\r\n          </p>\r\n        </div>\r\n        <Progress value={progress} className=\"h-2\" />\r\n        <p className=\"text-sm text-muted-foreground\">\r\n          {Math.round(progress)}% complete\r\n        </p>\r\n\r\n        <Separator className=\"my-6\" />\r\n        <div className=\"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0\">\r\n          <aside className=\"-mx-4 lg:w-1/6 pb-12\">\r\n            <SidebarNav\r\n              items={sidebarNavItems}\r\n              activeSection={activeSection}\r\n              setActiveSection={setActiveSection}\r\n            />\r\n          </aside>\r\n          <div className=\"flex justify-center w-full\">\r\n            <div className=\"flex-1 lg:max-w-2xl pb-12\">\r\n              {profileLoading ? (\r\n                <div className=\"flex flex-col items-center justify-center py-12\">\r\n                  <svg\r\n                    className=\"animate-spin h-10 w-10 text-black mb-4\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                  >\r\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\r\n                    <path\r\n                      className=\"opacity-75\"\r\n                      fill=\"currentColor\"\r\n                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n                    />\r\n                  </svg>\r\n                  <p className=\"text-gray-600\">Loading profile information...</p>\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-6\">\r\n                  <div>\r\n                    <h3 className=\"text-lg font-medium\">\r\n                      {activeSection === 'personal-info' && 'Personal Information'}\r\n                      {activeSection === 'other-info' && 'Other Information'}\r\n                    </h3>\r\n                    <p className=\"text-sm text-muted-foreground\">\r\n                      {activeSection === 'personal-info' && 'Enter your basic personal details, contact information, medium, standard, photo and documents'}\r\n                      {activeSection === 'other-info' && 'Provide additional details like Aadhaar number, blood group, birth place, etc.'}\r\n                    </p>\r\n                  </div>\r\n                  <Separator />\r\n\r\n                  <Form {...form}>\r\n                    <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-8\">\r\n\r\n                    {/* Personal Information Section */}\r\n                    {activeSection === 'personal-info' && (\r\n                      <div className=\"space-y-6\">\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"firstName\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">First Name *</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter First Name\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"middleName\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Middle Name</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Middle Name\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"lastName\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Last Name *</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Last Name\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"mothersName\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Mothers Name</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Mother's Name\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                        </div>\r\n                        <FormField\r\n                          control={form.control}\r\n                          name=\"email\"\r\n                          render={({ field }) => (\r\n                            <FormItem>\r\n                              <FormLabel className=\"text-black font-medium\">Email</FormLabel>\r\n                              <FormControl>\r\n                                <Input\r\n                                  {...field}\r\n                                  className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                  placeholder=\"Enter Email\"\r\n                                  type=\"email\"\r\n                                />\r\n                              </FormControl>\r\n                              <FormMessage className=\"text-red-500\" />\r\n                            </FormItem>\r\n                          )}\r\n                        />\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"contact\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Contact Number *</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"8520369851\"\r\n                                    type=\"tel\"\r\n                                    inputMode=\"numeric\"\r\n                                    pattern=\"[0-9]*\"\r\n                                    onKeyDown={(e) => {\r\n                                      const specialKeys = [\r\n                                        'Backspace',\r\n                                        'Tab',\r\n                                        'Enter',\r\n                                        'Escape',\r\n                                        'Delete',\r\n                                        'ArrowLeft',\r\n                                        'ArrowRight',\r\n                                        'Home',\r\n                                        'End',\r\n                                      ];\r\n                                      if (specialKeys.includes(e.key)) {\r\n                                        return;\r\n                                      }\r\n                                      if (e.ctrlKey && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase())) {\r\n                                        return;\r\n                                      }\r\n                                      if (!/^\\d$/.test(e.key)) {\r\n                                        e.preventDefault();\r\n                                      }\r\n                                    }}\r\n                                    onChange={(e) => {\r\n                                      const value = e.target.value.replace(/\\D/g, '');\r\n                                      field.onChange(value);\r\n                                    }}\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"contact2\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Contact Number 2</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Alternate Number\"\r\n                                    type=\"tel\"\r\n                                    inputMode=\"numeric\"\r\n                                    pattern=\"[0-9]*\"\r\n                                    onKeyDown={(e) => {\r\n                                      const specialKeys = [\r\n                                        'Backspace',\r\n                                        'Tab',\r\n                                        'Enter',\r\n                                        'Escape',\r\n                                        'Delete',\r\n                                        'ArrowLeft',\r\n                                        'ArrowRight',\r\n                                        'Home',\r\n                                        'End',\r\n                                      ];\r\n                                      if (specialKeys.includes(e.key)) {\r\n                                        return;\r\n                                      }\r\n                                      if (e.ctrlKey && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase())) {\r\n                                        return;\r\n                                      }\r\n                                      if (!/^\\d$/.test(e.key)) {\r\n                                        e.preventDefault();\r\n                                      }\r\n                                    }}\r\n                                    onChange={(e) => {\r\n                                      const value = e.target.value.replace(/\\D/g, '');\r\n                                      field.onChange(value);\r\n                                    }}\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                        </div>\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"gender\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Gender</FormLabel>\r\n                                <Select\r\n                                  onValueChange={field.onChange}\r\n                                  value={field.value || undefined}\r\n                                >\r\n                                  <FormControl>\r\n                                    <SelectTrigger className=\"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full\">\r\n                                      <SelectValue placeholder=\"Select\" />\r\n                                    </SelectTrigger>\r\n                                  </FormControl>\r\n                                  <SelectContent className=\"bg-white w-[var(--radix-select-trigger-width)]\">\r\n                                    <SelectItem value=\"male\">Male</SelectItem>\r\n                                    <SelectItem value=\"female\">Female</SelectItem>\r\n                                    <SelectItem value=\"other\">Other</SelectItem>\r\n                                  </SelectContent>\r\n                                </Select>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"age\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Age</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Age\"\r\n                                    type=\"number\"\r\n                                    min=\"1\"\r\n                                    max=\"100\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"birthday\"\r\n                            render={({ field }) => (\r\n                              <FormItem className=\"flex flex-col\">\r\n                                <FormLabel className=\"text-black font-medium\">Date of Birth *</FormLabel>\r\n                              <Popover>\r\n                                <PopoverTrigger asChild>\r\n                                  <FormControl>\r\n                                    <Button\r\n                                      variant=\"outline\"\r\n                                      className={cn(\r\n                                        'w-full pl-3 text-left font-normal bg-white border border-gray-300 hover:bg-gray-50 rounded-lg',\r\n                                        !field.value && 'text-muted-foreground'\r\n                                      )}\r\n                                    >\r\n                                      {field.value && field.value instanceof Date && !isNaN(field.value.getTime()) ? (\r\n                                        format(field.value, 'PPP')\r\n                                      ) : (\r\n                                        <span>Select your birthday</span>\r\n                                      )}\r\n                                      <CalendarIcon className=\"ml-auto h-4 w-4 opacity-50\" />\r\n                                    </Button>\r\n                                  </FormControl>\r\n                                </PopoverTrigger>\r\n                                <PopoverContent className=\"w-auto p-0 bg-white border border-gray-300 shadow-lg\" align=\"start\">\r\n                                  <div className=\"p-3 border-b border-gray-200\">\r\n                                    <div className=\"flex gap-2 mb-3\">\r\n                                      <Select\r\n                                        value={field.value ? field.value.getFullYear().toString() : \"\"}\r\n                                        onValueChange={(year) => {\r\n                                          const currentDate = field.value || new Date();\r\n                                          const newDate = new Date(currentDate);\r\n                                          newDate.setFullYear(parseInt(year));\r\n                                          field.onChange(newDate);\r\n                                        }}\r\n                                      >\r\n                                        <SelectTrigger className=\"w-24\">\r\n                                          <SelectValue placeholder=\"Year\" />\r\n                                        </SelectTrigger>\r\n                                        <SelectContent className=\"max-h-48\">\r\n                                          {Array.from({ length: 125 }, (_, i) => {\r\n                                            const year = new Date().getFullYear() - i;\r\n                                            return (\r\n                                              <SelectItem key={year} value={year.toString()}>\r\n                                                {year}\r\n                                              </SelectItem>\r\n                                            );\r\n                                          })}\r\n                                        </SelectContent>\r\n                                      </Select>\r\n                                      <Select\r\n                                        value={field.value ? field.value.getMonth().toString() : \"\"}\r\n                                        onValueChange={(month) => {\r\n                                          const currentDate = field.value || new Date();\r\n                                          const newDate = new Date(currentDate);\r\n                                          newDate.setMonth(parseInt(month));\r\n                                          field.onChange(newDate);\r\n                                        }}\r\n                                      >\r\n                                        <SelectTrigger className=\"w-32\">\r\n                                          <SelectValue placeholder=\"Month\" />\r\n                                        </SelectTrigger>\r\n                                        <SelectContent>\r\n                                          {[\r\n                                            'January', 'February', 'March', 'April', 'May', 'June',\r\n                                            'July', 'August', 'September', 'October', 'November', 'December'\r\n                                          ].map((month, index) => (\r\n                                            <SelectItem key={index} value={index.toString()}>\r\n                                              {month}\r\n                                            </SelectItem>\r\n                                          ))}\r\n                                        </SelectContent>\r\n                                      </Select>\r\n                                    </div>\r\n                                  </div>\r\n                                  <Calendar\r\n                                    mode=\"single\"\r\n                                    selected={field.value}\r\n                                    onSelect={field.onChange}\r\n                                    disabled={(date) => date > new Date() || date < new Date('1900-01-01')}\r\n                                    month={field.value || new Date()}\r\n                                    className=\"rounded-md border-0\"\r\n                                  />\r\n                                </PopoverContent>\r\n                              </Popover>\r\n                              <FormDescription className=\"text-xs text-gray-500\">\r\n                                Your date of birth will be verified with your documents\r\n                              </FormDescription>\r\n                              <FormMessage className=\"text-red-500\" />\r\n                            </FormItem>\r\n                          )}\r\n                        />\r\n                        </div>\r\n                        <FormField\r\n                          control={form.control}\r\n                          name=\"address\"\r\n                          render={({ field }) => (\r\n                            <FormItem>\r\n                              <FormLabel className=\"text-black font-medium\">Address *</FormLabel>\r\n                              <FormControl>\r\n                                <Textarea\r\n                                  {...field}\r\n                                  rows={3}\r\n                                  className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg resize-none\"\r\n                                  placeholder=\"Enter your full address\"\r\n                                />\r\n                              </FormControl>\r\n                              <FormDescription className=\"text-xs text-gray-500\">\r\n                                Provide your complete residential address\r\n                              </FormDescription>\r\n                              <FormMessage className=\"text-red-500\" />\r\n                            </FormItem>\r\n                          )}\r\n                        />\r\n                        <FormField\r\n                          control={form.control}\r\n                          name=\"school\"\r\n                          render={({ field }) => (\r\n                            <FormItem>\r\n                              <FormLabel className=\"text-black font-medium\">School Name *</FormLabel>\r\n                              <FormControl>\r\n                                <Input\r\n                                  {...field}\r\n                                  className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                  placeholder=\"Enter School\"\r\n                                />\r\n                              </FormControl>\r\n                              <FormMessage className=\"text-red-500\" />\r\n                            </FormItem>\r\n                          )}\r\n                        />\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"medium\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Medium *</FormLabel>\r\n                                <Select\r\n                                  onValueChange={(value) => {\r\n                                    field.onChange(value);\r\n                                  }}\r\n                                  value={field.value || undefined}\r\n                                >\r\n                                  <FormControl>\r\n                                    <SelectTrigger className=\"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full\">\r\n                                      <SelectValue placeholder=\"Select\" />\r\n                                    </SelectTrigger>\r\n                                  </FormControl>\r\n                                  <SelectContent className=\"bg-white w-[var(--radix-select-trigger-width)]\">\r\n                                    <SelectItem value=\"english\">English</SelectItem>\r\n                                    <SelectItem value=\"gujarati\">Gujarati</SelectItem>\r\n                                  </SelectContent>\r\n                                </Select>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"classroom\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Standard *</FormLabel>\r\n                                <Select\r\n                                  onValueChange={(value) => {\r\n                                    field.onChange(value);\r\n                                  }}\r\n                                  value={field.value || undefined}\r\n                                >\r\n                                  <FormControl>\r\n                                    <SelectTrigger className=\"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full\">\r\n                                      <SelectValue placeholder=\"Select\" />\r\n                                    </SelectTrigger>\r\n                                  </FormControl>\r\n                                  <SelectContent className=\"bg-white w-[var(--radix-select-trigger-width)]\">\r\n                                    {profileLoading ? (\r\n                                      <div className=\"flex items-center justify-center p-4\">\r\n                                        <svg\r\n                                          className=\"animate-spin h-5 w-5 text-black\"\r\n                                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                                          fill=\"none\"\r\n                                          viewBox=\"0 0 24 24\"\r\n                                        >\r\n                                          <circle\r\n                                            className=\"opacity-25\"\r\n                                            cx=\"12\"\r\n                                            cy=\"12\"\r\n                                            r=\"10\"\r\n                                            stroke=\"currentColor\"\r\n                                            strokeWidth=\"4\"\r\n                                          />\r\n                                          <path\r\n                                            className=\"opacity-75\"\r\n                                            fill=\"currentColor\"\r\n                                            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n                                          />\r\n                                        </svg>\r\n                                      </div>\r\n                                    ) : classroomOptions.length > 0 ? (\r\n                                      classroomOptions.map((option) => (\r\n                                        <SelectItem key={option.id} value={option.value}>\r\n                                          {option.value}\r\n                                        </SelectItem>\r\n                                      ))\r\n                                    ) : (\r\n                                      <div className=\"p-2 text-center text-gray-500\">No classroom options available</div>\r\n                                    )}\r\n                                  </SelectContent>\r\n                                </Select>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                        </div>\r\n                              <FormField\r\n                          control={form.control}\r\n                          name=\"photo\"\r\n                          render={() => (\r\n                            <Card className=\"shadow-lg border-0\">\r\n                              <CardHeader className=\"bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg\">\r\n                                <CardTitle className=\"text-lg font-medium text-gray-800\">Student Image *</CardTitle>\r\n                                <CardDescription className=\"text-gray-600\">\r\n                                  Take a clear photo of your face for your profile (Only jpg, jpeg, png allowed - MAX. 5MB)\r\n                                </CardDescription>\r\n                              </CardHeader>\r\n                          <CardContent>\r\n                            <FormItem>\r\n                              <FormControl>\r\n                                <div>\r\n                                  {cameraError && (\r\n                                    <div className=\"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\">\r\n                                      <p className=\"text-red-700 text-sm\">{cameraError}</p>\r\n                                    </div>\r\n                                  )}\r\n                                  {!isCameraOpen && !photo && (\r\n                                    <Button\r\n                                      type=\"button\"\r\n                                      onClick={openCamera}\r\n                                      className=\"w-full bg-black text-white font-medium py-6 rounded-lg flex items-center justify-center gap-2\"\r\n                                    >\r\n                                      <Camera className=\"h-5 w-5 mr-2\" />\r\n                                      Open Camera\r\n                                    </Button>\r\n                                  )}\r\n                                  {isCameraOpen && (\r\n                                    <div className=\"camera-container border border-gray-200 rounded-lg overflow-hidden shadow-sm\">\r\n                                      <video\r\n                                        ref={videoRef}\r\n                                        autoPlay\r\n                                        playsInline\r\n                                        className=\"w-full h-auto transform scale-x-[-1]\"\r\n                                      />\r\n                                      <div className=\"flex p-4 bg-gray-50\">\r\n                                        <Button\r\n                                          type=\"button\"\r\n                                          onClick={capturePhoto}\r\n                                          variant=\"default\"\r\n                                          className=\"flex-1 mr-2 bg-black hover:bg-gray-800 text-white\"\r\n                                        >\r\n                                          <Check className=\"h-4 w-4 mr-2\" />\r\n                                          Capture\r\n                                        </Button>\r\n                                        <Button\r\n                                          type=\"button\"\r\n                                          onClick={closeCamera}\r\n                                          variant=\"outline\"\r\n                                          className=\"flex-1 border-gray-300\"\r\n                                        >\r\n                                          <X className=\"h-4 w-4 mr-2\" />\r\n                                          Cancel\r\n                                        </Button>\r\n                                      </div>\r\n                                    </div>\r\n                                  )}\r\n                                  {!isCameraOpen && (profileData?.profile?.photo || photo) && (\r\n                                    <div className=\"flex flex-col sm:flex-row items-center gap-4\">\r\n                                      <div className=\"border rounded-lg shadow-md bg-gray-50 p-4 max-w-full\">\r\n                                        <div className=\"flex justify-center\">\r\n                                          {(() => {\r\n                                            const displayPhoto = photo || profileData?.profile?.photo;\r\n                                            if (displayPhoto) {\r\n                                              return (\r\n                                                <Image\r\n                                                  src={\r\n                                                    displayPhoto.startsWith('data:')\r\n                                                      ? displayPhoto\r\n                                                      : displayPhoto.startsWith('http')\r\n                                                      ? displayPhoto\r\n                                                      : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${displayPhoto}?t=${new Date().getTime()}`\r\n                                                  }\r\n                                                  alt=\"Student Photo\"\r\n                                                  height={1000}\r\n                                                  width={1000}\r\n                                                  className=\"max-w-full max-h-80 object-contain rounded-lg\"\r\n                                                  style={{ height: 'auto', width: 'auto' }}\r\n                                                  unoptimized={displayPhoto.startsWith('data:')}\r\n                                                />\r\n                                              );\r\n                                            }\r\n                                            return (\r\n                                              <div className=\"flex items-center justify-center h-32 w-48 bg-gray-100 rounded-lg\">\r\n                                                <Camera className=\"h-12 w-12 text-gray-400\" />\r\n                                              </div>\r\n                                            );\r\n                                          })()}\r\n                                        </div>\r\n                                      </div>\r\n                                      <Button\r\n                                        type=\"button\"\r\n                                        onClick={() => {\r\n                                          setPhoto(null);\r\n                                          setCameraError(null);\r\n                                          dispatch(updateProfilePhoto(undefined));\r\n                                          form.setValue('photo', null);\r\n                                          openCamera();\r\n                                        }}\r\n                                        variant=\"outline\"\r\n                                        className=\"border-gray-300\"\r\n                                      >\r\n                                        <Camera className=\"h-4 w-4 mr-2\" />\r\n                                        Retake Photo\r\n                                      </Button>\r\n                                    </div>\r\n                                  )}\r\n                                  <canvas ref={canvasRef} style={{ display: 'none' }} />\r\n                                </div>\r\n                              </FormControl>\r\n                              <FormDescription className=\"text-xs text-gray-500 mt-2\">\r\n                                A clear photo helps us identify you and personalize your profile\r\n                              </FormDescription>\r\n                              <FormMessage className=\"text-red-500\" />\r\n                            </FormItem>\r\n                          </CardContent>\r\n                        </Card>\r\n                      )}\r\n                    />\r\n                    <FormField\r\n                      control={form.control}\r\n                      name=\"document\"\r\n                      render={({ field }) => (\r\n                        <Card className=\"shadow-lg border-0\">\r\n                          <CardHeader className=\"bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg\">\r\n                            <CardTitle className=\"text-lg font-medium text-gray-800\">Identity Document *</CardTitle>\r\n                            <CardDescription className=\"text-gray-600\">\r\n                              Upload Aadhar card, Bonafide certificate, Leaving certificate, ID card or any other document that contains your birthdate\r\n                            </CardDescription>\r\n                          </CardHeader>\r\n                          <CardContent>\r\n                            <FormItem>\r\n                              {!uploadedDocument ? (\r\n                                <FormControl>\r\n                                  <div className=\"flex items-center justify-center w-full\">\r\n                                    <label className=\"flex flex-col items-center justify-center w-full h-36 border-2 border-gray-200 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors\">\r\n                                      <div className=\"flex flex-col items-center justify-center pt-5 pb-6\">\r\n                                        <Upload className=\"w-10 h-10 mb-3 text-black\" />\r\n                                        <p className=\"mb-2 text-sm text-gray-700\">\r\n                                          <span className=\"font-semibold\">Click to upload</span> or drag and drop\r\n                                        </p>\r\n                                        <p className=\"text-xs text-gray-500\">PDF, PNG, JPG or JPEG (MAX. 5MB)</p>\r\n                                      </div>\r\n                                      <Input\r\n                                        id=\"document\"\r\n                                        type=\"file\"\r\n                                        accept=\".pdf,.jpg,.jpeg,.png\"\r\n                                        className=\"hidden\"\r\n                                        onChange={(e) => {\r\n                                          const file = e.target.files?.[0];\r\n                                          if (file) {\r\n                                            if (file.size > 5 * 1024 * 1024) {\r\n                                              toast.error('File size exceeds 5MB limit');\r\n                                              return;\r\n                                            }\r\n                                            const documentWithUrl = {\r\n                                              name: file.name,\r\n                                              size: file.size,\r\n                                              type: file.type,\r\n                                              url: URL.createObjectURL(file),\r\n                                            };\r\n                                            setUploadedDocument(documentWithUrl);\r\n                                            setIsDocumentRemoved(false);\r\n                                            field.onChange(file);\r\n                                          }\r\n                                        }}\r\n                                      />\r\n                                    </label>\r\n                                  </div>\r\n                                </FormControl>\r\n                              ) : (\r\n                                <div className=\"bg-gray-50 rounded-lg p-4 border border-gray-200\">\r\n                                  <div className=\"flex items-center justify-between\">\r\n                                    <div className=\"flex items-center space-x-3\">\r\n                                      <div className=\"p-2 bg-[#fff8f3] rounded-full\">\r\n                                        <FileText className=\"h-5 w-5 text-black\" />\r\n                                      </div>\r\n                                      <div>\r\n                                        <p className=\"text-sm font-medium text-gray-700\">{uploadedDocument.name}</p>\r\n                                        <p className=\"text-xs text-gray-500\">\r\n                                          {uploadedDocument instanceof File\r\n                                            ? formatFileSize(uploadedDocument.size)\r\n                                            : 'Previously uploaded document'}\r\n                                        </p>\r\n                                      </div>\r\n                                    </div>\r\n                                    <div className=\"flex space-x-2\">\r\n                                      {uploadedDocument && 'url' in uploadedDocument && (\r\n                                        <Button\r\n                                          type=\"button\"\r\n                                          variant=\"outline\"\r\n                                          size=\"sm\"\r\n                                          onClick={() => window.open(uploadedDocument.url, '_blank')}\r\n                                          className=\"h-8 px-3 border-gray-200\"\r\n                                        >\r\n                                          View\r\n                                        </Button>\r\n                                      )}\r\n                                      <Button\r\n                                        type=\"button\"\r\n                                        variant=\"outline\"\r\n                                        size=\"sm\"\r\n                                        onClick={removeDocument}\r\n                                        className=\"h-8 w-8 p-0 border-gray-200\"\r\n                                      >\r\n                                        <X className=\"h-4 w-4 text-gray-500\" />\r\n                                      </Button>\r\n                                    </div>\r\n                                  </div>\r\n                                </div>\r\n                              )}\r\n                              <FormDescription className=\"text-xs text-gray-500 mt-2\">\r\n                                This document will serve to verify your identity and date of birth.\r\n                              </FormDescription>\r\n                              <FormMessage className=\"text-red-500\" />\r\n                            </FormItem>\r\n                          </CardContent>\r\n                        </Card>\r\n                      )}\r\n                    />\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* Other Information Section */}\r\n                    {activeSection === 'other-info' && (\r\n                      <div className=\"space-y-6\">\r\n                       <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"aadhaarNumber\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Aadhaar Number</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Aadhaar No\"\r\n                                    type=\"tel\"\r\n                                    inputMode=\"numeric\"\r\n                                    pattern=\"[0-9]*\"\r\n                                    maxLength={12}\r\n                                    onKeyDown={(e) => {\r\n                                      const specialKeys = [\r\n                                        'Backspace',\r\n                                        'Tab',\r\n                                        'Enter',\r\n                                        'Escape',\r\n                                        'Delete',\r\n                                        'ArrowLeft',\r\n                                        'ArrowRight',\r\n                                        'Home',\r\n                                        'End',\r\n                                      ];\r\n                                      if (specialKeys.includes(e.key)) {\r\n                                        return;\r\n                                      }\r\n                                      if (e.ctrlKey && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase())) {\r\n                                        return;\r\n                                      }\r\n                                      if (!/^\\d$/.test(e.key)) {\r\n                                        e.preventDefault();\r\n                                      }\r\n                                    }}\r\n                                    onChange={(e) => {\r\n                                      const value = e.target.value.replace(/\\D/g, '');\r\n                                      field.onChange(value);\r\n                                    }}\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"bloodGroup\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Blood Group</FormLabel>\r\n                                <Select\r\n                                  onValueChange={field.onChange}\r\n                                  value={field.value || undefined}\r\n                                >\r\n                                  <FormControl>\r\n                                    <SelectTrigger className=\"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full\">\r\n                                      <SelectValue placeholder=\"Select\" />\r\n                                    </SelectTrigger>\r\n                                  </FormControl>\r\n                                  <SelectContent className=\"bg-white w-[var(--radix-select-trigger-width)]\">\r\n                                    <SelectItem value=\"A+\">A+</SelectItem>\r\n                                    <SelectItem value=\"A-\">A-</SelectItem>\r\n                                    <SelectItem value=\"B+\">B+</SelectItem>\r\n                                    <SelectItem value=\"B-\">B-</SelectItem>\r\n                                    <SelectItem value=\"AB+\">AB+</SelectItem>\r\n                                    <SelectItem value=\"AB-\">AB-</SelectItem>\r\n                                    <SelectItem value=\"O+\">O+</SelectItem>\r\n                                    <SelectItem value=\"O-\">O-</SelectItem>\r\n                                  </SelectContent>\r\n                                </Select>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"birthPlace\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Birth Place</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Birth Place\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"motherTongue\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Mother Tongue</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Mother Tongue\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"religion\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Religion</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Religion\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"caste\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Caste</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Caste\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"subCaste\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel className=\"text-black font-medium\">Sub Caste</FormLabel>\r\n                                <FormControl>\r\n                                  <Input\r\n                                    {...field}\r\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\r\n                                    placeholder=\"Enter Sub Caste\"\r\n                                  />\r\n                                </FormControl>\r\n                                <FormMessage className=\"text-red-500\" />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* Submit Button */}\r\n                    <div className=\"flex justify-end pt-6 border-t border-gray-100\">\r\n                      <Button\r\n                        type=\"submit\"\r\n                        disabled={isSubmitting}\r\n                        className=\"bg-black text-white hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed px-8 py-3 font-medium transition-all duration-200\"\r\n                      >\r\n                        {isSubmitting ? (\r\n                          <div className=\"flex items-center gap-2\">\r\n                            <svg className=\"animate-spin h-4 w-4\" viewBox=\"0 0 24 24\">\r\n                              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\r\n                              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\r\n                            </svg>\r\n                            Saving...\r\n                          </div>\r\n                        ) : profileData ? (\r\n                          'Update Profile'\r\n                        ) : (\r\n                          'Save Profile'\r\n                        )}\r\n                      </Button>\r\n                    </div>\r\n\r\n                    </form>\r\n                  </Form>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <Footer />\r\n    </>\r\n  );\r\n};\r\n\r\nconst StudentProfilePage = () => {\r\n  return (\r\n    <Suspense\r\n      fallback={\r\n        <div className=\"min-h-screen flex items-center justify-center\">\r\n          <svg\r\n            className=\"animate-spin h-10 w-10 text-black\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n          >\r\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\r\n            <path\r\n              className=\"opacity-75\"\r\n              fill=\"currentColor\"\r\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n            />\r\n          </svg>\r\n        </div>\r\n      }\r\n    >\r\n      <StudentProfileContent />\r\n    </Suspense>\r\n  );\r\n};\r\n\r\nexport default StudentProfilePage;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AASA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA1CA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,MAAM,oBAAoB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,uCAAuC,QAAQ,GAAG,EAAE,CAAC,oIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IACvF,SAAS,oIAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,IAAI,8CACR,GAAG,CAAC,IAAI,6CACR,KAAK,CAAC,SAAS;IAClB,UAAU,oIAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,IAAI,8CACR,GAAG,CAAC,IAAI,6CACR,KAAK,CAAC,SAAS,4CACf,QAAQ,GACR,EAAE,CAAC,oIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,UAAU,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAE,gBAAgB;IAAmC;IACtE,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,KAAK,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACxB,eAAe,oIAAA,CAAA,IAAC,CACb,MAAM,GACN,GAAG,CAAC,IAAI,qCACR,GAAG,CAAC,IAAI,qCACR,KAAK,CAAC,SAAS,4CACf,QAAQ,GACR,EAAE,CAAC,oIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,OAAO,oIAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;IACvB,UAAU,oIAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;AAC5B;AAIA,MAAM,kBAAkB;IACtB;QACE,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,MAAM;IACR;CACD;AAED,MAAM,wBAAwB;IAC5B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,aAAa,GAAG,CAAC,YAAY;IAC9C,MAAM,SAAS,aAAa,GAAG,CAAC;IAEhC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAErD;IACF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,EAAE,WAAW,EAAE,SAAS,cAAc,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EACzD,CAAC,QAAqB,MAAM,cAAc;IAG5C,MAAM,UAAU,aAAa,WAAW;IACxC,MAAM,mBAAmB,aAAa,oBAAoB,EAAE;IAE5D,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAqB;QACtC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,WAAW;YACX,YAAY;YACZ,UAAU;YACV,aAAa;YACb,OAAO;YACP,SAAS;YACT,UAAU;YACV,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,SAAS;YACT,KAAK;YACL,eAAe;YACf,YAAY;YACZ,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;YACP,UAAU;QACZ;QACA,MAAM;IACR;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC;QAC1C,IAAI,CAAC,cAAc;YACjB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC;QAC1C,IAAI,cAAc;YAChB,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;QAC7B;IACF,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,SAAS;YACxB,MAAM,UAAU,YAAY,OAAO;YACnC,IAAI,oBAAoB;YACxB,MAAM,gBAAgB;YAEtB,IAAI,QAAQ,OAAO,EAAE,aAAa,QAAQ,OAAO,EAAE,YAAY,QAAQ,OAAO,EAAE,WAC5E,QAAQ,QAAQ,IAAI,QAAQ,MAAM,IAAI,QAAQ,OAAO,EAAE;gBACzD;YACF;YAEA,IAAI,QAAQ,MAAM,IAAI,QAAQ,SAAS,EAAE;gBACvC;YACF;YAEA,IAAI,QAAQ,KAAK,IAAI,QAAQ,WAAW,EAAE;gBACxC;YACF;YAEA,YAAY,AAAC,oBAAoB,gBAAiB;QACpD;IACF,GAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa;QAElB,MAAM,aAAa,YAAY,OAAO;QACtC,MAAM,cAAc,YAAY,WAAW,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,mBAAmB;QAE9F,MAAM,aAAa;YACjB,WAAW,aAAa,aAAa;YACrC,YAAY,aAAa,cAAc;YACvC,UAAU,aAAa,YAAY;YACnC,aAAa,aAAa,eAAe;YACzC,OAAO,aAAa,SAAS;YAC7B,SAAS,aAAa,WAAW;YACjC,UAAU,YAAY,cAAc;YACpC,QAAQ,YAAY,UAAU;YAC9B,WAAW,YAAY,aAAa;YACpC,QAAQ,YAAY,UAAU;YAC9B,UAAU,YAAY,WAAW,IAAI,KAAK,WAAW,QAAQ,IAAI;YACjE,QAAQ,YAAY,UAAU;YAC9B,SAAS,YAAY,WAAW;YAChC,KAAK,YAAY,KAAK,cAAc;YACpC,eAAe,YAAY,aAAa;YACxC,YAAY,YAAY,cAAc;YACtC,YAAY,YAAY,cAAc;YACtC,cAAc,YAAY,gBAAgB;YAC1C,UAAU,YAAY,YAAY;YAClC,OAAO,YAAY,SAAS;YAC5B,UAAU,YAAY,YAAY;QACpC;QAEA,IAAI,YAAY,SAAS,CAAC,OAAO;YAC/B,SAAS,WAAW,KAAK;YACzB,KAAK,QAAQ,CAAC,SAAS,WAAW,KAAK;QACzC;QAEA,IAAI,YAAY,eAAe,CAAC,oBAAoB,CAAC,mBAAmB;YACtE,MAAM,UAAU,8DAAwC;YACxD,MAAM,cAAc,WAAW,WAAW,CAAC,UAAU,CAAC,UAClD,WAAW,WAAW,GACtB,GAAG,UAAU,WAAW,WAAW,EAAE;YAEzC,MAAM,cAAc;gBAClB,MAAM,YAAY,KAAK,CAAC,KAAK,GAAG,MAAM;gBACtC,MAAM;gBACN,KAAK;gBACL,MAAM;YACR;YAEA,oBAAoB;YACpB,KAAK,QAAQ,CAAC,YAAY;QAC5B;QAEA,MAAM,gBAAgB,KAAK,SAAS;QACpC,MAAM,cAAc,CAAC,cAAc,SAAS,IAAI,CAAC,cAAc,QAAQ,IAAI,CAAC,cAAc,OAAO;QACjG,MAAM,2BAA2B,CAAC,cAAc,MAAM,IAAI,CAAC,cAAc,SAAS;QAElF,IAAI,eAAe,0BAA0B;YAC3C,KAAK,KAAK,CAAC;QACb;IACF,GAAG;QAAC;QAAa;QAAM;QAAO;QAAkB;KAAkB;IAElE,MAAM,aAAa;QACjB,eAAe;QAEf,IAAI;YACF,IAAI,CAAC,UAAU,YAAY,EAAE,cAAc;gBACzC,MAAM,IAAI,MAAM;YAClB;YAEA,gBAAgB;YAEhB,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBACvD,OAAO;oBAAE,YAAY;gBAAO;YAC9B;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,SAAS,GAAG;gBAC7B,SAAS,OAAO,CAAC,gBAAgB,GAAG;oBAClC,SAAS,OAAO,EAAE,OAAO,MAAM,IAAM,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACnD;YACF;QACF,EAAE,OAAO,OAAY;YACnB,gBAAgB;YAChB,MAAM,UAAU,MAAM,IAAI,KAAK,oBAC3B,yDACA;YACJ,eAAe;YACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,gBAAgB,CAAC,QAA2B,WAAmB,GAAG,EAAE,UAAkB,GAAG;QAC7F,MAAM,UAAU,OAAO,UAAU,CAAC;QAClC,IAAI,CAAC,SAAS,OAAO;QAErB,MAAM,gBAAgB,OAAO,KAAK;QAClC,MAAM,iBAAiB,OAAO,MAAM;QAEpC,IAAI,WAAW;QACf,IAAI,YAAY;QAEhB,IAAI,gBAAgB,UAAU;YAC5B,WAAW;YACX,YAAY,AAAC,iBAAiB,WAAY;QAC5C;QAEA,MAAM,mBAAmB,SAAS,aAAa,CAAC;QAChD,iBAAiB,KAAK,GAAG;QACzB,iBAAiB,MAAM,GAAG;QAE1B,MAAM,oBAAoB,iBAAiB,UAAU,CAAC;QACtD,IAAI,CAAC,mBAAmB,OAAO;QAE/B,kBAAkB,SAAS,CAAC,QAAQ,GAAG,GAAG,UAAU;QAEpD,OAAO,iBAAiB,SAAS,CAAC,cAAc;IAClD;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,UAAU,OAAO,EAAE;QAE7C,MAAM,QAAQ,SAAS,OAAO;QAC9B,MAAM,SAAS,UAAU,OAAO;QAChC,MAAM,UAAU,OAAO,UAAU,CAAC;QAElC,OAAO,KAAK,GAAG,MAAM,UAAU;QAC/B,OAAO,MAAM,GAAG,MAAM,WAAW;QAEjC,SAAS,UAAU,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QACpD,SAAS;QACT,SAAS,MAAM,CAAC,GAAG;QACnB,SAAS,UAAU,OAAO,CAAC,OAAO,KAAK,EAAE,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QACvE,SAAS;QAET,MAAM,yBAAyB,cAAc,QAAQ,KAAK;QAC1D,MAAM,aAAa,uBAAuB,KAAK,CAAC,IAAI,CAAC,EAAE;QACvD,MAAM,WAAW,AAAC,WAAW,MAAM,GAAG,IAAK,IAAI;QAE/C,IAAI,WAAW,MAAM;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,SAAS;QACT,KAAK,QAAQ,CAAC,SAAS;QACvB,SAAS,CAAA,GAAA,6IAAA,CAAA,qBAAkB,AAAD,EAAE;QAE5B;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,SAAS,OAAO,EAAE,WAAW;YAC/B,MAAM,SAAS,SAAS,OAAO,CAAC,SAAS;YACzC,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YAC9C,SAAS,OAAO,CAAC,SAAS,GAAG;QAC/B;QACA,gBAAgB;QAChB,eAAe;IACjB;IAEA,MAAM,iBAAiB;QACrB,IAAI,oBAAoB,SAAS,oBAAoB,iBAAiB,GAAG,CAAC,UAAU,CAAC,UAAU;YAC7F,IAAI,eAAe,CAAC,iBAAiB,GAAG;QAC1C;QAEA,oBAAoB;QACpB,qBAAqB;QACrB,MAAM,YAAY,SAAS,cAAc,CAAC;QAC1C,IAAI,WAAW,UAAU,KAAK,GAAG;QACjC,KAAK,QAAQ,CAAC,YAAY;IAC5B;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,QAAQ,MAAM,OAAO,QAAQ;aAC5B,IAAI,QAAQ,SAAS,OAAO,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,KAAK;aACxD,OAAO,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,KAAK;IAC7C;IAIA,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAEhB,IAAI;YACF,MAAM,eAAe,SAAS,aAAa,SAAS;YACpD,IAAI,CAAC,cAAc;gBACjB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,gBAAgB;gBAChB;YACF;YAEA,IAAI,CAAC,oBAAoB,mBAAmB;gBAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,gBAAgB;gBAChB;YACF;YAEA,IAAI,CAAE,MAAM,KAAK,OAAO,IAAK;gBAC3B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,gBAAgB;gBAChB;YACF;YAEA,MAAM,WAAgB;gBACpB,WAAW,KAAK,SAAS;gBACzB,YAAY,KAAK,UAAU;gBAC3B,UAAU,KAAK,QAAQ;gBACvB,aAAa,KAAK,WAAW;gBAC7B,OAAO,KAAK,KAAK;gBACjB,SAAS,KAAK,OAAO;gBACrB,UAAU,KAAK,QAAQ;gBACvB,QAAQ,KAAK,MAAM;gBACnB,WAAW,KAAK,SAAS;gBACzB,QAAQ,KAAK,MAAM;gBACnB,UAAU,KAAK,QAAQ,EAAE,iBAAiB;gBAC1C,QAAQ,KAAK,MAAM;gBACnB,SAAS,KAAK,OAAO;gBACrB,KAAK,KAAK,GAAG;gBACb,eAAe,KAAK,aAAa;gBACjC,YAAY,KAAK,UAAU;gBAC3B,YAAY,KAAK,UAAU;gBAC3B,cAAc,KAAK,YAAY;gBAC/B,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;YACzB;YAEA,IAAI,OAAO,WAAW,UAAU;gBAC9B,MAAM,aAAa,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;gBACtC,MAAM,WAAW,AAAC,WAAW,MAAM,GAAG,IAAK,IAAI;gBAE/C,IAAI,WAAW,MAAM;oBACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ;gBACF;gBAEA,SAAS,KAAK,GAAG;gBACjB,SAAS,aAAa,GAAG;YAC3B;YAEA,IAAI,4BAA4B,QAAS,oBAAoB,SAAS,oBAAoB,iBAAiB,GAAG,CAAC,UAAU,CAAC,UAAW;gBACnI,MAAM,eAAe,4BAA4B,OAC7C,mBACA,MAAM,MAAM,iBAAiB,GAAG,EAC7B,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,IACpB,IAAI,CAAC,CAAA,OAAQ,IAAI,KAAK;wBAAC;qBAAK,EAAE,iBAAiB,IAAI,EAAE;wBAAE,MAAM,iBAAiB,IAAI;oBAAC;gBAE1F,MAAM,iBAAiB,MAAM,IAAI,QAAgB,CAAC,SAAS;oBACzD,MAAM,SAAS,IAAI;oBACnB,OAAO,MAAM,GAAG,IAAM,QAAQ,AAAC,OAAO,MAAM,CAAY,KAAK,CAAC,IAAI,CAAC,EAAE;oBACrE,OAAO,OAAO,GAAG;oBACjB,OAAO,aAAa,CAAC;gBACvB;gBAEA,MAAM,YAAY,AAAC,eAAe,MAAM,GAAG,IAAK,IAAI;gBACpD,IAAI,YAAY,MAAM;oBACpB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ;gBACF;gBAEA,SAAS,QAAQ,GAAG;gBACpB,SAAS,gBAAgB,GAAG,aAAa,IAAI;gBAC7C,SAAS,YAAY,GAAG,aAAa,IAAI;YAC3C;YAEA,0BAA0B;YAC1B,IAAI,qBAAqB,aAAa,SAAS,aAAa;gBAC1D,SAAS,cAAc,GAAG;YAC5B;YAEA,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,CAAC,cAAc;gBACjB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,CAAA,GAAA,8IAAA,CAAA,uBAAoB,AAAD,EAAE;YAEnD,IAAI,OAAO,IAAI,CAAC,aAAa,KAAK,aAAa;gBAC7C,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,UAAU,YAAY,UAAU,cAAc,CAAC;gBAExE,MAAM,sBAAsB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,mBAAmB;gBAC/E,MAAM,cAAc;oBAClB,GAAG,mBAAmB;oBACtB,IAAI,oBAAoB,EAAE,IAAI,aAAa,SAAS,SAAS,MAAM;oBACnE,WAAW,KAAK,SAAS;oBACzB,YAAY,KAAK,UAAU;oBAC3B,UAAU,KAAK,QAAQ;oBACvB,aAAa,KAAK,WAAW;oBAC7B,OAAO,KAAK,KAAK,IAAI,oBAAoB,KAAK,IAAI,aAAa,SAAS,SAAS,SAAS;oBAC1F,SAAS,KAAK,OAAO;gBACvB;gBAEA,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gBACpD,qBAAqB;gBACrB,MAAM,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;gBAEjC,IAAI,UAAU;oBACZ,IAAI,QAAQ;wBACV,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,QAAQ;oBACrC,OAAO;wBACL,OAAO,IAAI,CAAC;oBACd;gBACF;YACF,OAAO,IAAI,OAAO,IAAI,CAAC,aAAa,KAAK,YAAY;gBACnD,MAAM,eAAe,OAAO,OAAO;gBAEnC,IAAI,aAAa,QAAQ,CAAC,UAAU,aAAa,QAAQ,CAAC,iBAAiB;oBACzE,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,aAAa,UAAU,CAAC;oBACxB,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gBAAgB;gBAC9B;YACF;QACF,EAAE,OAAM;YACN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE;;0BACE,8OAAC,mIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAGlD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,OAAO;wBAAU,WAAU;;;;;;kCACrC,8OAAC;wBAAE,WAAU;;4BACV,KAAK,KAAK,CAAC;4BAAU;;;;;;;kCAGxB,8OAAC,qIAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CACf,cAAA,8OAAC,iKAAA,CAAA,aAAU;oCACT,OAAO;oCACP,eAAe;oCACf,kBAAkB;;;;;;;;;;;0CAGtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ,+BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAU;gDACV,OAAM;gDACN,MAAK;gDACL,SAAQ;;kEAER,8OAAC;wDAAO,WAAU;wDAAa,IAAG;wDAAK,IAAG;wDAAK,GAAE;wDAAK,QAAO;wDAAe,aAAY;;;;;;kEACxF,8OAAC;wDACC,WAAU;wDACV,MAAK;wDACL,GAAE;;;;;;;;;;;;0DAGN,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;6DAG/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;;4DACX,kBAAkB,mBAAmB;4DACrC,kBAAkB,gBAAgB;;;;;;;kEAErC,8OAAC;wDAAE,WAAU;;4DACV,kBAAkB,mBAAmB;4DACrC,kBAAkB,gBAAgB;;;;;;;;;;;;;0DAGvC,8OAAC,qIAAA,CAAA,YAAS;;;;;0DAEV,8OAAC,gIAAA,CAAA,OAAI;gDAAE,GAAG,IAAI;0DACZ,cAAA,8OAAC;oDAAK,UAAU,KAAK,YAAY,CAAC;oDAAW,WAAU;;wDAGtD,kBAAkB,iCACjB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;;;;;;;;;;;sGAGhB,8OAAC,gIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;;;;;;;;;;;sGAGhB,8OAAC,gIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;;;;;;;;;;;sGAGhB,8OAAC,gIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;;;;;;;;;;;sGAGhB,8OAAC,gIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8EAK/B,8OAAC,gIAAA,CAAA,YAAS;oEACR,SAAS,KAAK,OAAO;oEACrB,MAAK;oEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;8FACP,8OAAC,gIAAA,CAAA,YAAS;oFAAC,WAAU;8FAAyB;;;;;;8FAC9C,8OAAC,gIAAA,CAAA,cAAW;8FACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;wFACH,GAAG,KAAK;wFACT,WAAU;wFACV,aAAY;wFACZ,MAAK;;;;;;;;;;;8FAGT,8OAAC,gIAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;;;;;;;;;;;;8EAI7B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;gGACZ,MAAK;gGACL,WAAU;gGACV,SAAQ;gGACR,WAAW,CAAC;oGACV,MAAM,cAAc;wGAClB;wGACA;wGACA;wGACA;wGACA;wGACA;wGACA;wGACA;wGACA;qGACD;oGACD,IAAI,YAAY,QAAQ,CAAC,EAAE,GAAG,GAAG;wGAC/B;oGACF;oGACA,IAAI,EAAE,OAAO,IAAI;wGAAC;wGAAK;wGAAK;wGAAK;qGAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,WAAW,KAAK;wGACnE;oGACF;oGACA,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE,GAAG,GAAG;wGACvB,EAAE,cAAc;oGAClB;gGACF;gGACA,UAAU,CAAC;oGACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;oGAC5C,MAAM,QAAQ,CAAC;gGACjB;;;;;;;;;;;sGAGJ,8OAAC,gIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;gGACZ,MAAK;gGACL,WAAU;gGACV,SAAQ;gGACR,WAAW,CAAC;oGACV,MAAM,cAAc;wGAClB;wGACA;wGACA;wGACA;wGACA;wGACA;wGACA;wGACA;wGACA;qGACD;oGACD,IAAI,YAAY,QAAQ,CAAC,EAAE,GAAG,GAAG;wGAC/B;oGACF;oGACA,IAAI,EAAE,OAAO,IAAI;wGAAC;wGAAK;wGAAK;wGAAK;qGAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,WAAW,KAAK;wGACnE;oGACF;oGACA,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE,GAAG,GAAG;wGACvB,EAAE,cAAc;oGAClB;gGACF;gGACA,UAAU,CAAC;oGACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;oGAC5C,MAAM,QAAQ,CAAC;gGACjB;;;;;;;;;;;sGAGJ,8OAAC,gIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8EAK/B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,8OAAC,kIAAA,CAAA,SAAM;4FACL,eAAe,MAAM,QAAQ;4FAC7B,OAAO,MAAM,KAAK,IAAI;;8GAEtB,8OAAC,gIAAA,CAAA,cAAW;8GACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;wGAAC,WAAU;kHACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4GAAC,aAAY;;;;;;;;;;;;;;;;8GAG7B,8OAAC,kIAAA,CAAA,gBAAa;oGAAC,WAAU;;sHACvB,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAO;;;;;;sHACzB,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAS;;;;;;sHAC3B,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAQ;;;;;;;;;;;;;;;;;;sGAG9B,8OAAC,gIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;gGACZ,MAAK;gGACL,KAAI;gGACJ,KAAI;;;;;;;;;;;sGAGR,8OAAC,gIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;oFAAC,WAAU;;sGAClB,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAChD,8OAAC,mIAAA,CAAA,UAAO;;8GACN,8OAAC,mIAAA,CAAA,iBAAc;oGAAC,OAAO;8GACrB,cAAA,8OAAC,gIAAA,CAAA,cAAW;kHACV,cAAA,8OAAC,kIAAA,CAAA,SAAM;4GACL,SAAQ;4GACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iGACA,CAAC,MAAM,KAAK,IAAI;;gHAGjB,MAAM,KAAK,IAAI,MAAM,KAAK,YAAY,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,OAAO,MACvE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,KAAK,EAAE,uBAEpB,8OAAC;8HAAK;;;;;;8HAER,8OAAC,0MAAA,CAAA,WAAY;oHAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8GAI9B,8OAAC,mIAAA,CAAA,iBAAc;oGAAC,WAAU;oGAAuD,OAAM;;sHACrF,8OAAC;4GAAI,WAAU;sHACb,cAAA,8OAAC;gHAAI,WAAU;;kIACb,8OAAC,kIAAA,CAAA,SAAM;wHACL,OAAO,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,KAAK;wHAC5D,eAAe,CAAC;4HACd,MAAM,cAAc,MAAM,KAAK,IAAI,IAAI;4HACvC,MAAM,UAAU,IAAI,KAAK;4HACzB,QAAQ,WAAW,CAAC,SAAS;4HAC7B,MAAM,QAAQ,CAAC;wHACjB;;0IAEA,8OAAC,kIAAA,CAAA,gBAAa;gIAAC,WAAU;0IACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oIAAC,aAAY;;;;;;;;;;;0IAE3B,8OAAC,kIAAA,CAAA,gBAAa;gIAAC,WAAU;0IACtB,MAAM,IAAI,CAAC;oIAAE,QAAQ;gIAAI,GAAG,CAAC,GAAG;oIAC/B,MAAM,OAAO,IAAI,OAAO,WAAW,KAAK;oIACxC,qBACE,8OAAC,kIAAA,CAAA,aAAU;wIAAY,OAAO,KAAK,QAAQ;kJACxC;uIADc;;;;;gIAIrB;;;;;;;;;;;;kIAGJ,8OAAC,kIAAA,CAAA,SAAM;wHACL,OAAO,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,QAAQ,GAAG,QAAQ,KAAK;wHACzD,eAAe,CAAC;4HACd,MAAM,cAAc,MAAM,KAAK,IAAI,IAAI;4HACvC,MAAM,UAAU,IAAI,KAAK;4HACzB,QAAQ,QAAQ,CAAC,SAAS;4HAC1B,MAAM,QAAQ,CAAC;wHACjB;;0IAEA,8OAAC,kIAAA,CAAA,gBAAa;gIAAC,WAAU;0IACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oIAAC,aAAY;;;;;;;;;;;0IAE3B,8OAAC,kIAAA,CAAA,gBAAa;0IACX;oIACC;oIAAW;oIAAY;oIAAS;oIAAS;oIAAO;oIAChD;oIAAQ;oIAAU;oIAAa;oIAAW;oIAAY;iIACvD,CAAC,GAAG,CAAC,CAAC,OAAO,sBACZ,8OAAC,kIAAA,CAAA,aAAU;wIAAa,OAAO,MAAM,QAAQ;kJAC1C;uIADc;;;;;;;;;;;;;;;;;;;;;;;;;;;sHAQ3B,8OAAC,oIAAA,CAAA,WAAQ;4GACP,MAAK;4GACL,UAAU,MAAM,KAAK;4GACrB,UAAU,MAAM,QAAQ;4GACxB,UAAU,CAAC,OAAS,OAAO,IAAI,UAAU,OAAO,IAAI,KAAK;4GACzD,OAAO,MAAM,KAAK,IAAI,IAAI;4GAC1B,WAAU;;;;;;;;;;;;;;;;;;sGAIhB,8OAAC,gIAAA,CAAA,kBAAe;4FAAC,WAAU;sGAAwB;;;;;;sGAGnD,8OAAC,gIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8EAK7B,8OAAC,gIAAA,CAAA,YAAS;oEACR,SAAS,KAAK,OAAO;oEACrB,MAAK;oEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;8FACP,8OAAC,gIAAA,CAAA,YAAS;oFAAC,WAAU;8FAAyB;;;;;;8FAC9C,8OAAC,gIAAA,CAAA,cAAW;8FACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wFACN,GAAG,KAAK;wFACT,MAAM;wFACN,WAAU;wFACV,aAAY;;;;;;;;;;;8FAGhB,8OAAC,gIAAA,CAAA,kBAAe;oFAAC,WAAU;8FAAwB;;;;;;8FAGnD,8OAAC,gIAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;;;;;;;;;;;;8EAI7B,8OAAC,gIAAA,CAAA,YAAS;oEACR,SAAS,KAAK,OAAO;oEACrB,MAAK;oEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;8FACP,8OAAC,gIAAA,CAAA,YAAS;oFAAC,WAAU;8FAAyB;;;;;;8FAC9C,8OAAC,gIAAA,CAAA,cAAW;8FACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;wFACH,GAAG,KAAK;wFACT,WAAU;wFACV,aAAY;;;;;;;;;;;8FAGhB,8OAAC,gIAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;;;;;;;;;;;;8EAI7B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,8OAAC,kIAAA,CAAA,SAAM;4FACL,eAAe,CAAC;gGACd,MAAM,QAAQ,CAAC;4FACjB;4FACA,OAAO,MAAM,KAAK,IAAI;;8GAEtB,8OAAC,gIAAA,CAAA,cAAW;8GACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;wGAAC,WAAU;kHACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4GAAC,aAAY;;;;;;;;;;;;;;;;8GAG7B,8OAAC,kIAAA,CAAA,gBAAa;oGAAC,WAAU;;sHACvB,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAU;;;;;;sHAC5B,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAW;;;;;;;;;;;;;;;;;;sGAGjC,8OAAC,gIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,8OAAC,kIAAA,CAAA,SAAM;4FACL,eAAe,CAAC;gGACd,MAAM,QAAQ,CAAC;4FACjB;4FACA,OAAO,MAAM,KAAK,IAAI;;8GAEtB,8OAAC,gIAAA,CAAA,cAAW;8GACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;wGAAC,WAAU;kHACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4GAAC,aAAY;;;;;;;;;;;;;;;;8GAG7B,8OAAC,kIAAA,CAAA,gBAAa;oGAAC,WAAU;8GACtB,+BACC,8OAAC;wGAAI,WAAU;kHACb,cAAA,8OAAC;4GACC,WAAU;4GACV,OAAM;4GACN,MAAK;4GACL,SAAQ;;8HAER,8OAAC;oHACC,WAAU;oHACV,IAAG;oHACH,IAAG;oHACH,GAAE;oHACF,QAAO;oHACP,aAAY;;;;;;8HAEd,8OAAC;oHACC,WAAU;oHACV,MAAK;oHACL,GAAE;;;;;;;;;;;;;;;;iHAIN,iBAAiB,MAAM,GAAG,IAC5B,iBAAiB,GAAG,CAAC,CAAC,uBACpB,8OAAC,kIAAA,CAAA,aAAU;4GAAiB,OAAO,OAAO,KAAK;sHAC5C,OAAO,KAAK;2GADE,OAAO,EAAE;;;;oIAK5B,8OAAC;wGAAI,WAAU;kHAAgC;;;;;;;;;;;;;;;;;sGAIrD,8OAAC,gIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8EAKzB,8OAAC,gIAAA,CAAA,YAAS;oEACd,SAAS,KAAK,OAAO;oEACrB,MAAK;oEACL,QAAQ,kBACN,8OAAC,gIAAA,CAAA,OAAI;4EAAC,WAAU;;8FACd,8OAAC,gIAAA,CAAA,aAAU;oFAAC,WAAU;;sGACpB,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAoC;;;;;;sGACzD,8OAAC,gIAAA,CAAA,kBAAe;4FAAC,WAAU;sGAAgB;;;;;;;;;;;;8FAIjD,8OAAC,gIAAA,CAAA,cAAW;8FACV,cAAA,8OAAC,gIAAA,CAAA,WAAQ;;0GACP,8OAAC,gIAAA,CAAA,cAAW;0GACV,cAAA,8OAAC;;wGACE,6BACC,8OAAC;4GAAI,WAAU;sHACb,cAAA,8OAAC;gHAAE,WAAU;0HAAwB;;;;;;;;;;;wGAGxC,CAAC,gBAAgB,CAAC,uBACjB,8OAAC,kIAAA,CAAA,SAAM;4GACL,MAAK;4GACL,SAAS;4GACT,WAAU;;8HAEV,8OAAC,sMAAA,CAAA,SAAM;oHAAC,WAAU;;;;;;gHAAiB;;;;;;;wGAItC,8BACC,8OAAC;4GAAI,WAAU;;8HACb,8OAAC;oHACC,KAAK;oHACL,QAAQ;oHACR,WAAW;oHACX,WAAU;;;;;;8HAEZ,8OAAC;oHAAI,WAAU;;sIACb,8OAAC,kIAAA,CAAA,SAAM;4HACL,MAAK;4HACL,SAAS;4HACT,SAAQ;4HACR,WAAU;;8IAEV,8OAAC,oMAAA,CAAA,QAAK;oIAAC,WAAU;;;;;;gIAAiB;;;;;;;sIAGpC,8OAAC,kIAAA,CAAA,SAAM;4HACL,MAAK;4HACL,SAAS;4HACT,SAAQ;4HACR,WAAU;;8IAEV,8OAAC,4LAAA,CAAA,IAAC;oIAAC,WAAU;;;;;;gIAAiB;;;;;;;;;;;;;;;;;;;wGAMrC,CAAC,gBAAgB,CAAC,aAAa,SAAS,SAAS,KAAK,mBACrD,8OAAC;4GAAI,WAAU;;8HACb,8OAAC;oHAAI,WAAU;8HACb,cAAA,8OAAC;wHAAI,WAAU;kIACZ,CAAC;4HACA,MAAM,eAAe,SAAS,aAAa,SAAS;4HACpD,IAAI,cAAc;gIAChB,qBACE,8OAAC,6HAAA,CAAA,UAAK;oIACJ,KACE,aAAa,UAAU,CAAC,WACpB,eACA,aAAa,UAAU,CAAC,UACxB,eACA,GAAG,8DAAwC,2BAA2B,aAAa,GAAG,EAAE,IAAI,OAAO,OAAO,IAAI;oIAEpH,KAAI;oIACJ,QAAQ;oIACR,OAAO;oIACP,WAAU;oIACV,OAAO;wIAAE,QAAQ;wIAAQ,OAAO;oIAAO;oIACvC,aAAa,aAAa,UAAU,CAAC;;;;;;4HAG3C;4HACA,qBACE,8OAAC;gIAAI,WAAU;0IACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oIAAC,WAAU;;;;;;;;;;;wHAGxB,CAAC;;;;;;;;;;;8HAGL,8OAAC,kIAAA,CAAA,SAAM;oHACL,MAAK;oHACL,SAAS;wHACP,SAAS;wHACT,eAAe;wHACf,SAAS,CAAA,GAAA,6IAAA,CAAA,qBAAkB,AAAD,EAAE;wHAC5B,KAAK,QAAQ,CAAC,SAAS;wHACvB;oHACF;oHACA,SAAQ;oHACR,WAAU;;sIAEV,8OAAC,sMAAA,CAAA,SAAM;4HAAC,WAAU;;;;;;wHAAiB;;;;;;;;;;;;;sHAKzC,8OAAC;4GAAO,KAAK;4GAAW,OAAO;gHAAE,SAAS;4GAAO;;;;;;;;;;;;;;;;;0GAGrD,8OAAC,gIAAA,CAAA,kBAAe;gGAAC,WAAU;0GAA6B;;;;;;0GAGxD,8OAAC,gIAAA,CAAA,cAAW;gGAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8EAMjC,8OAAC,gIAAA,CAAA,YAAS;oEACR,SAAS,KAAK,OAAO;oEACrB,MAAK;oEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,OAAI;4EAAC,WAAU;;8FACd,8OAAC,gIAAA,CAAA,aAAU;oFAAC,WAAU;;sGACpB,8OAAC,gIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAoC;;;;;;sGACzD,8OAAC,gIAAA,CAAA,kBAAe;4FAAC,WAAU;sGAAgB;;;;;;;;;;;;8FAI7C,8OAAC,gIAAA,CAAA,cAAW;8FACV,cAAA,8OAAC,gIAAA,CAAA,WAAQ;;4FACN,CAAC,iCACA,8OAAC,gIAAA,CAAA,cAAW;0GACV,cAAA,8OAAC;oGAAI,WAAU;8GACb,cAAA,8OAAC;wGAAM,WAAU;;0HACf,8OAAC;gHAAI,WAAU;;kIACb,8OAAC,sMAAA,CAAA,SAAM;wHAAC,WAAU;;;;;;kIAClB,8OAAC;wHAAE,WAAU;;0IACX,8OAAC;gIAAK,WAAU;0IAAgB;;;;;;4HAAsB;;;;;;;kIAExD,8OAAC;wHAAE,WAAU;kIAAwB;;;;;;;;;;;;0HAEvC,8OAAC,iIAAA,CAAA,QAAK;gHACJ,IAAG;gHACH,MAAK;gHACL,QAAO;gHACP,WAAU;gHACV,UAAU,CAAC;oHACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;oHAChC,IAAI,MAAM;wHACR,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;4HAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;4HACZ;wHACF;wHACA,MAAM,kBAAkB;4HACtB,MAAM,KAAK,IAAI;4HACf,MAAM,KAAK,IAAI;4HACf,MAAM,KAAK,IAAI;4HACf,KAAK,IAAI,eAAe,CAAC;wHAC3B;wHACA,oBAAoB;wHACpB,qBAAqB;wHACrB,MAAM,QAAQ,CAAC;oHACjB;gHACF;;;;;;;;;;;;;;;;;;;;;uHAMR,8OAAC;gGAAI,WAAU;0GACb,cAAA,8OAAC;oGAAI,WAAU;;sHACb,8OAAC;4GAAI,WAAU;;8HACb,8OAAC;oHAAI,WAAU;8HACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wHAAC,WAAU;;;;;;;;;;;8HAEtB,8OAAC;;sIACC,8OAAC;4HAAE,WAAU;sIAAqC,iBAAiB,IAAI;;;;;;sIACvE,8OAAC;4HAAE,WAAU;sIACV,4BAA4B,OACzB,eAAe,iBAAiB,IAAI,IACpC;;;;;;;;;;;;;;;;;;sHAIV,8OAAC;4GAAI,WAAU;;gHACZ,oBAAoB,SAAS,kCAC5B,8OAAC,kIAAA,CAAA,SAAM;oHACL,MAAK;oHACL,SAAQ;oHACR,MAAK;oHACL,SAAS,IAAM,OAAO,IAAI,CAAC,iBAAiB,GAAG,EAAE;oHACjD,WAAU;8HACX;;;;;;8HAIH,8OAAC,kIAAA,CAAA,SAAM;oHACL,MAAK;oHACL,SAAQ;oHACR,MAAK;oHACL,SAAS;oHACT,WAAU;8HAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wHAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0GAMvB,8OAAC,gIAAA,CAAA,kBAAe;gGAAC,WAAU;0GAA6B;;;;;;0GAGxD,8OAAC,gIAAA,CAAA,cAAW;gGAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wDAUhC,kBAAkB,8BACjB,8OAAC;4DAAI,WAAU;sEACd,cAAA,8OAAC;gEAAI,WAAU;;kFACZ,8OAAC,gIAAA,CAAA,YAAS;wEACR,SAAS,KAAK,OAAO;wEACrB,MAAK;wEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kGACP,8OAAC,gIAAA,CAAA,YAAS;wFAAC,WAAU;kGAAyB;;;;;;kGAC9C,8OAAC,gIAAA,CAAA,cAAW;kGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4FACH,GAAG,KAAK;4FACT,WAAU;4FACV,aAAY;4FACZ,MAAK;4FACL,WAAU;4FACV,SAAQ;4FACR,WAAW;4FACX,WAAW,CAAC;gGACV,MAAM,cAAc;oGAClB;oGACA;oGACA;oGACA;oGACA;oGACA;oGACA;oGACA;oGACA;iGACD;gGACD,IAAI,YAAY,QAAQ,CAAC,EAAE,GAAG,GAAG;oGAC/B;gGACF;gGACA,IAAI,EAAE,OAAO,IAAI;oGAAC;oGAAK;oGAAK;oGAAK;iGAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,WAAW,KAAK;oGACnE;gGACF;gGACA,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE,GAAG,GAAG;oGACvB,EAAE,cAAc;gGAClB;4FACF;4FACA,UAAU,CAAC;gGACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;gGAC5C,MAAM,QAAQ,CAAC;4FACjB;;;;;;;;;;;kGAGJ,8OAAC,gIAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;;;;;;;;;;;;kFAI7B,8OAAC,gIAAA,CAAA,YAAS;wEACR,SAAS,KAAK,OAAO;wEACrB,MAAK;wEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kGACP,8OAAC,gIAAA,CAAA,YAAS;wFAAC,WAAU;kGAAyB;;;;;;kGAC9C,8OAAC,kIAAA,CAAA,SAAM;wFACL,eAAe,MAAM,QAAQ;wFAC7B,OAAO,MAAM,KAAK,IAAI;;0GAEtB,8OAAC,gIAAA,CAAA,cAAW;0GACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oGAAC,WAAU;8GACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wGAAC,aAAY;;;;;;;;;;;;;;;;0GAG7B,8OAAC,kIAAA,CAAA,gBAAa;gGAAC,WAAU;;kHACvB,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAK;;;;;;kHACvB,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAK;;;;;;kHACvB,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAK;;;;;;kHACvB,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAK;;;;;;kHACvB,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAM;;;;;;kHACxB,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAM;;;;;;kHACxB,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAK;;;;;;kHACvB,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAK;;;;;;;;;;;;;;;;;;kGAG3B,8OAAC,gIAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;;;;;;;;;;;;kFAI7B,8OAAC,gIAAA,CAAA,YAAS;wEACR,SAAS,KAAK,OAAO;wEACrB,MAAK;wEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kGACP,8OAAC,gIAAA,CAAA,YAAS;wFAAC,WAAU;kGAAyB;;;;;;kGAC9C,8OAAC,gIAAA,CAAA,cAAW;kGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4FACH,GAAG,KAAK;4FACT,WAAU;4FACV,aAAY;;;;;;;;;;;kGAGhB,8OAAC,gIAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;;;;;;;;;;;;kFAI7B,8OAAC,gIAAA,CAAA,YAAS;wEACR,SAAS,KAAK,OAAO;wEACrB,MAAK;wEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kGACP,8OAAC,gIAAA,CAAA,YAAS;wFAAC,WAAU;kGAAyB;;;;;;kGAC9C,8OAAC,gIAAA,CAAA,cAAW;kGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4FACH,GAAG,KAAK;4FACT,WAAU;4FACV,aAAY;;;;;;;;;;;kGAGhB,8OAAC,gIAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;;;;;;;;;;;;kFAI7B,8OAAC,gIAAA,CAAA,YAAS;wEACR,SAAS,KAAK,OAAO;wEACrB,MAAK;wEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kGACP,8OAAC,gIAAA,CAAA,YAAS;wFAAC,WAAU;kGAAyB;;;;;;kGAC9C,8OAAC,gIAAA,CAAA,cAAW;kGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4FACH,GAAG,KAAK;4FACT,WAAU;4FACV,aAAY;;;;;;;;;;;kGAGhB,8OAAC,gIAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;;;;;;;;;;;;kFAI7B,8OAAC,gIAAA,CAAA,YAAS;wEACR,SAAS,KAAK,OAAO;wEACrB,MAAK;wEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kGACP,8OAAC,gIAAA,CAAA,YAAS;wFAAC,WAAU;kGAAyB;;;;;;kGAC9C,8OAAC,gIAAA,CAAA,cAAW;kGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4FACH,GAAG,KAAK;4FACT,WAAU;4FACV,aAAY;;;;;;;;;;;kGAGhB,8OAAC,gIAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;;;;;;;;;;;;kFAI7B,8OAAC,gIAAA,CAAA,YAAS;wEACR,SAAS,KAAK,OAAO;wEACrB,MAAK;wEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kGACP,8OAAC,gIAAA,CAAA,YAAS;wFAAC,WAAU;kGAAyB;;;;;;kGAC9C,8OAAC,gIAAA,CAAA,cAAW;kGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4FACH,GAAG,KAAK;4FACT,WAAU;4FACV,aAAY;;;;;;;;;;;kGAGhB,8OAAC,gIAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEASnC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,UAAU;gEACV,WAAU;0EAET,6BACC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;4EAAuB,SAAQ;;8FAC5C,8OAAC;oFAAO,WAAU;oFAAa,IAAG;oFAAK,IAAG;oFAAK,GAAE;oFAAK,QAAO;oFAAe,aAAY;;;;;;8FACxF,8OAAC;oFAAK,WAAU;oFAAa,MAAK;oFAAe,GAAE;;;;;;;;;;;;wEAC/C;;;;;;2EAGN,cACF,mBAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAapB,8OAAC,mIAAA,CAAA,UAAM;;;;;;;AAGb;AAEA,MAAM,qBAAqB;IACzB,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QACP,wBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;;;;;;kBAMV,cAAA,8OAAC;;;;;;;;;;AAGP;uCAEe", "debugId": null}}]}